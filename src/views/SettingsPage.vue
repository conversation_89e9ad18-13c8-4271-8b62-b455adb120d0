<template>
    <div class="settings-page">
        <div class="page-header">
            <h2 class="page-title">系统设置</h2>
            <p class="page-description">配置应用参数和个人偏好</p>
        </div>

        <div class="settings-content">
            <NTabs type="line" animated>
                <NTabPane name="general" tab="常规设置">
                    <div class="settings-section">
                        <h3>界面设置</h3>
                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">主题模式</span>
                                <span class="setting-description"
                                    >选择应用的主题外观</span
                                >
                            </div>
                            <NSelect
                                v-model:value="currentTheme"
                                :options="themeOptions"
                                style="width: 150px"
                            />
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">语言</span>
                                <span class="setting-description"
                                    >选择界面显示语言</span
                                >
                                <div
                                    style="
                                        font-size: 12px;
                                        color: #666;
                                        margin-top: 4px;
                                    "
                                >
                                    Settings:
                                    {{ settingsStore.settings.language }} |
                                    I18n: {{ i18nStore.currentLanguage }} |
                                    Sample: {{ i18nStore.t.settings.title }}
                                </div>
                            </div>
                            <div
                                style="
                                    display: flex;
                                    gap: 8px;
                                    align-items: center;
                                "
                            >
                                <NSelect
                                    v-model:value="currentLanguage"
                                    :options="languageOptions"
                                    style="width: 150px"
                                />
                                <NButton
                                    size="small"
                                    @click="testLanguageSwitch"
                                >
                                    测试切换
                                </NButton>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label"
                                    >侧边栏默认状态</span
                                >
                                <span class="setting-description"
                                    >设置侧边栏的默认展开状态</span
                                >
                            </div>
                            <NSwitch
                                v-model:value="
                                    settingsStore.settings.sidebarExpanded
                                "
                            />
                        </div>
                    </div>

                    <NDivider />

                    <div class="settings-section">
                        <h3>数据设置</h3>
                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">自动保存</span>
                                <span class="setting-description"
                                    >自动保存投资组合和设置</span
                                >
                            </div>
                            <NSwitch
                                v-model:value="settingsStore.settings.autoSave"
                            />
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">数据缓存时间</span>
                                <span class="setting-description"
                                    >股票数据的缓存有效期（分钟）</span
                                >
                            </div>
                            <NInputNumber
                                v-model:value="settingsStore.settings.cacheTime"
                                :min="1"
                                :max="1440"
                                style="width: 150px"
                            />
                        </div>
                    </div>
                </NTabPane>

                <NTabPane name="optimization" tab="优化设置">
                    <div class="settings-section">
                        <h3>默认优化参数</h3>
                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">默认时间窗口</span>
                                <span class="setting-description"
                                    >计算历史数据的默认天数</span
                                >
                            </div>
                            <NInputNumber
                                v-model:value="
                                    settingsStore.settings.defaultWindowDays
                                "
                                :min="30"
                                :max="1000"
                                style="width: 150px"
                            />
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label"
                                    >默认最大迭代次数</span
                                >
                                <span class="setting-description"
                                    >优化算法的默认最大迭代次数</span
                                >
                            </div>
                            <NInputNumber
                                v-model:value="
                                    settingsStore.settings.defaultMaxIterations
                                "
                                :min="100"
                                :max="10000"
                                :step="100"
                                style="width: 150px"
                            />
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label"
                                    >默认收益率计算方式</span
                                >
                                <span class="setting-description"
                                    >计算股票收益率的默认方法</span
                                >
                            </div>
                            <NSelect
                                v-model:value="
                                    settingsStore.settings.defaultProfitType
                                "
                                :options="profitTypeOptions"
                                style="width: 150px"
                            />
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">无风险利率</span>
                                <span class="setting-description"
                                    >用于计算夏普比率的无风险利率</span
                                >
                            </div>
                            <NInputNumber
                                v-model:value="
                                    settingsStore.settings.riskFreeRate
                                "
                                :min="0"
                                :max="0.1"
                                :step="0.001"
                                :precision="3"
                                style="width: 150px"
                            />
                        </div>
                    </div>
                </NTabPane>

                <NTabPane name="data" tab="数据管理">
                    <div class="settings-section">
                        <h3>数据导入导出</h3>
                        <div class="data-actions">
                            <NCard title="导出数据" size="small">
                                <p>导出所有投资组合和设置数据</p>
                                <NButton @click="exportData" type="primary">
                                    <template #icon>
                                        <NIcon>
                                            <Download />
                                        </NIcon>
                                    </template>
                                    导出数据
                                </NButton>
                            </NCard>

                            <NCard title="导入数据" size="small">
                                <p>从文件导入投资组合和设置数据</p>
                                <NUpload
                                    :show-file-list="false"
                                    accept=".json"
                                    @change="importData"
                                >
                                    <NButton>
                                        <template #icon>
                                            <NIcon>
                                                <CloudUpload />
                                            </NIcon>
                                        </template>
                                        选择文件
                                    </NButton>
                                </NUpload>
                            </NCard>

                            <NCard title="清除数据" size="small">
                                <p>清除所有本地存储的数据</p>
                                <NButton
                                    @click="clearData"
                                    type="error"
                                    secondary
                                >
                                    <template #icon>
                                        <NIcon>
                                            <Trash />
                                        </NIcon>
                                    </template>
                                    清除数据
                                </NButton>
                            </NCard>
                        </div>
                    </div>
                </NTabPane>

                <NTabPane name="about" tab="关于">
                    <div class="settings-section">
                        <div class="about-content">
                            <div class="app-info">
                                <NIcon size="64" class="app-icon">
                                    <TrendingUp />
                                </NIcon>
                                <h2>Halloween Quant</h2>
                                <p class="version">版本 0.1.0</p>
                                <p class="description">
                                    基于现代投资组合理论的量化投资分析平台
                                </p>
                            </div>

                            <div class="tech-stack">
                                <h3>技术栈</h3>
                                <div class="tech-items">
                                    <NTag>Vue 3</NTag>
                                    <NTag>TypeScript</NTag>
                                    <NTag>Tauri</NTag>
                                    <NTag>Rust</NTag>
                                    <NTag>Naive UI</NTag>
                                    <NTag>Vite</NTag>
                                </div>
                            </div>

                            <div class="links">
                                <h3>相关链接</h3>
                                <div class="link-items">
                                    <NButton
                                        text
                                        @click="openLink('https://github.com')"
                                    >
                                        <template #icon>
                                            <NIcon>
                                                <LogoGithub />
                                            </NIcon>
                                        </template>
                                        GitHub
                                    </NButton>
                                    <NButton
                                        text
                                        @click="openLink('https://docs.rs')"
                                    >
                                        文档
                                    </NButton>
                                    <NButton
                                        text
                                        @click="
                                            openLink(
                                                'mailto:<EMAIL>',
                                            )
                                        "
                                    >
                                        反馈
                                    </NButton>
                                </div>
                            </div>
                        </div>
                    </div>
                </NTabPane>
            </NTabs>
        </div>

        <div class="settings-actions">
            <NButton @click="resetSettings">重置设置</NButton>
            <NButton type="primary" @click="saveSettings">保存设置</NButton>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted } from "vue";
import {
    NTabs,
    NTabPane,
    NSelect,
    NSwitch,
    NInputNumber,
    NDivider,
    NCard,
    NButton,
    NIcon,
    NUpload,
    NTag,
    useMessage,
    useDialog,
} from "naive-ui";
import {
    Download,
    CloudUpload,
    Trash,
    TrendingUp,
    LogoGithub,
} from "@vicons/ionicons5";
import { useSettingsStore } from "../stores/settings";
import { useI18nStore } from "../stores/i18n";

const message = useMessage();
const dialog = useDialog();
const settingsStore = useSettingsStore();
const i18nStore = useI18nStore();

// 主题设置的计算属性
const currentTheme = computed({
    get: () => settingsStore.settings.theme,
    set: (value: string) => {
        settingsStore.updateSettings({
            theme: value as "dark" | "light" | "auto",
        });
        updateTheme(value);
    },
});

// 语言设置的计算属性
const currentLanguage = computed({
    get: () => settingsStore.settings.language,
    set: (value: string) => {
        console.log("Language selector changed to:", value);
        settingsStore.updateSettings({ language: value as "zh-CN" | "en-US" });
        updateLanguage(value);
    },
});

// 设置数据现在通过 settingsStore 管理

// 选项数据
const themeOptions = [
    { label: "深色主题", value: "dark" },
    { label: "浅色主题", value: "light" },
    { label: "跟随系统", value: "auto" },
];

const languageOptions = [
    { label: "简体中文", value: "zh-CN" },
    { label: "English", value: "en-US" },
];

const profitTypeOptions = [
    { label: "对数收益率", value: "Logarithm" },
    { label: "简单收益率", value: "Simple" },
];

// 更新主题
const updateTheme = (theme: string) => {
    const themeLabels = {
        dark: "深色主题",
        light: "浅色主题",
        auto: "跟随系统",
    };
    message.info(
        `主题已切换为: ${themeLabels[theme as keyof typeof themeLabels] || theme}`,
    );
};

// 更新语言
const updateLanguage = (language: string) => {
    const languageLabels = {
        "zh-CN": "简体中文",
        "en-US": "English",
    };
    console.log("Language updated to:", language);
    console.log("Settings store language:", settingsStore.settings.language);
    console.log("I18n store language:", i18nStore.currentLanguage);
    message.info(
        `语言已切换为: ${languageLabels[language as keyof typeof languageLabels] || language}`,
    );
};

// 测试语言切换
const testLanguageSwitch = () => {
    const newLanguage =
        settingsStore.settings.language === "zh-CN" ? "en-US" : "zh-CN";
    console.log("Test: Switching language to", newLanguage);
    settingsStore.updateSettings({ language: newLanguage });

    // 延迟检查状态
    setTimeout(() => {
        console.log(
            "Test: After switch - Settings:",
            settingsStore.settings.language,
            "I18n:",
            i18nStore.currentLanguage,
        );
    }, 100);
};

// 导出数据
const exportData = () => {
    const data = {
        // settings: settings.value,
        portfolios: JSON.parse(localStorage.getItem("portfolios") || "[]"),
        recentSearches: JSON.parse(
            localStorage.getItem("recent-searches") || "[]",
        ),
        exportTime: new Date().toISOString(),
    };

    const jsonStr = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonStr], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `halloween-quant-data-${new Date().toISOString().split("T")[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    message.success("数据导出成功");
};

// 导入数据
const importData = (options: any) => {
    const file = options.file.file;
    const reader = new FileReader();

    reader.onload = (e) => {
        try {
            const data = JSON.parse(e.target?.result as string);

            // if (data.settings) {
            //     settings.value = { ...settings.value, ...data.settings };
            // }
            if (data.portfolios) {
                localStorage.setItem(
                    "portfolios",
                    JSON.stringify(data.portfolios),
                );
            }
            if (data.recentSearches) {
                localStorage.setItem(
                    "recent-searches",
                    JSON.stringify(data.recentSearches),
                );
            }

            message.success("数据导入成功");
        } catch (error) {
            message.error("数据格式错误，导入失败");
        }
    };

    reader.readAsText(file);
};

// 清除数据
const clearData = () => {
    dialog.warning({
        title: "确认清除",
        content:
            "此操作将清除所有本地数据，包括投资组合、设置和搜索记录。此操作不可撤销。",
        positiveText: "确认清除",
        negativeText: "取消",
        onPositiveClick: () => {
            settingsStore.clearAllData();
            message.success("数据已清除");
        },
    });
};

// 打开链接
const openLink = (url: string) => {
    window.open(url, "_blank");
};

// 重置设置
const resetSettings = () => {
    dialog.info({
        title: "重置设置",
        content: "确定要重置所有设置为默认值吗？",
        positiveText: "确认",
        negativeText: "取消",
        onPositiveClick: () => {
            settingsStore.resetSettings();
            message.success("设置已重置");
        },
    });
};

// 保存设置
const saveSettings = () => {
    settingsStore.saveSettings();
    message.success("设置已保存");
};

// 初始化
onMounted(() => {
    settingsStore.loadSettings();
});
</script>

<style scoped>
.settings-page {
    padding: 24px;
    max-width: 1000px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 32px;
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.page-description {
    font-size: 16px;
    color: var(--n-text-color-2);
    margin: 0;
}

.settings-content {
    margin-bottom: 32px;
}

.settings-section {
    margin-bottom: 32px;
}

.settings-section h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--n-border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
}

.setting-label {
    display: block;
    font-weight: 500;
    margin-bottom: 4px;
}

.setting-description {
    display: block;
    font-size: 13px;
    color: var(--n-text-color-2);
}

.data-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.about-content {
    text-align: center;
}

.app-info {
    margin-bottom: 32px;
}

.app-icon {
    color: #63e2b7;
    margin-bottom: 16px;
}

.app-info h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.version {
    font-size: 14px;
    color: var(--n-text-color-2);
    margin: 0 0 16px 0;
}

.description {
    font-size: 16px;
    color: var(--n-text-color-2);
    margin: 0;
    max-width: 400px;
    margin: 0 auto;
}

.tech-stack,
.links {
    margin-bottom: 24px;
}

.tech-stack h3,
.links h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.tech-items,
.link-items {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
}

.settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--n-border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .settings-page {
        padding: 16px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .data-actions {
        grid-template-columns: 1fr;
    }

    .settings-actions {
        flex-direction: column;
    }
}
</style>

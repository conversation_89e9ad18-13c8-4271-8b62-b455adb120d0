<template>
    <div class="search-page">
        <div class="search-header">
            <h2 class="page-title">股票搜索</h2>
            <p class="page-description">搜索并查看股票的详细分析数据</p>
        </div>

        <div class="search-section">
            <div class="search-container">
                <NInputGroup size="large">
                    <NAutoComplete
                        v-model:value="searchInput"
                        :options="options"
                        placeholder="输入股票代码或名称进行搜索..."
                        class="search-input"
                        clearable
                        @keyup.enter="searchStock"
                    />
                    <NButton 
                        type="primary" 
                        @click="searchStock"
                        :disabled="!searchInput.trim()"
                        class="search-button"
                    >
                        <template #icon>
                            <NIcon>
                                <Search />
                            </NIcon>
                        </template>
                        搜索
                    </NButton>
                </NInputGroup>
            </div>

            <!-- 搜索建议 -->
            <div v-if="searchInput && filteredSuggestions.length > 0" class="suggestions">
                <h4>搜索建议</h4>
                <div class="suggestion-list">
                    <div 
                        v-for="suggestion in filteredSuggestions.slice(0, 8)" 
                        :key="suggestion.value"
                        class="suggestion-item"
                        @click="selectSuggestion(suggestion)"
                    >
                        <div class="suggestion-code">{{ suggestion.value }}</div>
                        <div class="suggestion-name">{{ suggestion.label }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近搜索 -->
        <div v-if="recentSearches.length > 0" class="recent-section">
            <h3>最近搜索</h3>
            <div class="recent-list">
                <NTag 
                    v-for="recent in recentSearches" 
                    :key="recent"
                    class="recent-tag"
                    closable
                    @click="searchInput = recent; searchStock()"
                    @close="removeRecentSearch(recent)"
                >
                    {{ recent }}
                </NTag>
            </div>
        </div>

        <!-- 热门股票 -->
        <div class="popular-section">
            <h3>热门股票</h3>
            <div class="popular-grid">
                <div 
                    v-for="stock in popularStocks" 
                    :key="stock.code"
                    class="popular-item"
                    @click="goToStock(stock.code)"
                >
                    <div class="popular-code">{{ stock.code }}</div>
                    <div class="popular-name">{{ stock.name }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { 
    NInputGroup, 
    NAutoComplete, 
    NButton, 
    NIcon, 
    NTag,
    useMessage 
} from 'naive-ui';
import { Search } from '@vicons/ionicons5';
import { invoke } from '@tauri-apps/api/core';
import { useRouter } from 'vue-router';

const router = useRouter();
const message = useMessage();

type Stock = {
    code: string;
    name: string;
    exchangeCode: string;
    countryCode: string;
    type: string;
};

type AutoCompleteOption = {
    label: string;
    value: string;
};

// 搜索相关状态
const searchInput = ref('');
const rawOptions: AutoCompleteOption[] = [];
const options = ref<AutoCompleteOption[]>([]);
const recentSearches = ref<string[]>([]);

// 热门股票数据
const popularStocks = ref([
    { code: '000001', name: '平安银行' },
    { code: '000002', name: '万科A' },
    { code: '000858', name: '五粮液' },
    { code: '002415', name: '海康威视' },
    { code: '600036', name: '招商银行' },
    { code: '600519', name: '贵州茅台' }
]);

// 计算搜索建议
const filteredSuggestions = computed(() => {
    const input = searchInput.value.trim().toLowerCase();
    if (!input) return [];
    
    return rawOptions.filter(option => 
        option.label.toLowerCase().includes(input) || 
        option.value.toLowerCase().includes(input)
    );
});

// 初始化数据
onMounted(async () => {
    try {
        const stockList: Stock[] = await invoke('stock_list');
        
        // 添加股票名称选项
        for (const stock of stockList) {
            rawOptions.push({ label: stock.name, value: stock.code });
        }
        
        // 添加股票代码选项
        for (const stock of stockList) {
            rawOptions.push({ label: stock.code, value: stock.code });
        }
        
        // 加载最近搜索记录
        loadRecentSearches();
    } catch (error) {
        message.error('加载股票列表失败');
        console.error('Failed to load stock list:', error);
    }
});

// 搜索股票
const searchStock = () => {
    const query = searchInput.value.trim();
    if (!query) {
        message.warning('请输入搜索内容');
        return;
    }
    
    // 添加到最近搜索
    addRecentSearch(query);
    
    // 跳转到股票详情页
    router.push(`/stock/${query}`);
};

// 选择搜索建议
const selectSuggestion = (suggestion: AutoCompleteOption) => {
    searchInput.value = suggestion.value;
    searchStock();
};

// 直接跳转到股票页面
const goToStock = (code: string) => {
    addRecentSearch(code);
    router.push(`/stock/${code}`);
};

// 最近搜索管理
const loadRecentSearches = () => {
    const saved = localStorage.getItem('recent-searches');
    if (saved) {
        recentSearches.value = JSON.parse(saved);
    }
};

const addRecentSearch = (query: string) => {
    const recent = recentSearches.value.filter(item => item !== query);
    recent.unshift(query);
    recentSearches.value = recent.slice(0, 10); // 最多保存10个
    localStorage.setItem('recent-searches', JSON.stringify(recentSearches.value));
};

const removeRecentSearch = (query: string) => {
    recentSearches.value = recentSearches.value.filter(item => item !== query);
    localStorage.setItem('recent-searches', JSON.stringify(recentSearches.value));
};
</script>

<style scoped>
.search-page {
    padding: 24px;
    max-width: 800px;
    margin: 0 auto;
}

.search-header {
    text-align: center;
    margin-bottom: 32px;
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--n-text-color);
}

.page-description {
    font-size: 16px;
    color: var(--n-text-color-2);
    margin: 0;
}

.search-section {
    margin-bottom: 32px;
}

.search-container {
    margin-bottom: 16px;
}

.search-input {
    flex: 1;
}

.search-button {
    border-radius: 0 6px 6px 0;
}

.suggestions {
    margin-top: 16px;
}

.suggestions h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--n-text-color-2);
}

.suggestion-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
}

.suggestion-item {
    padding: 12px;
    border: 1px solid var(--n-border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-item:hover {
    border-color: var(--n-primary-color);
    background: var(--n-primary-color-hover);
}

.suggestion-code {
    font-weight: 600;
    font-size: 14px;
}

.suggestion-name {
    font-size: 12px;
    color: var(--n-text-color-2);
    margin-top: 4px;
}

.recent-section, .popular-section {
    margin-bottom: 32px;
}

.recent-section h3, .popular-section h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
}

.recent-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.recent-tag {
    cursor: pointer;
}

.popular-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
}

.popular-item {
    padding: 16px;
    border: 1px solid var(--n-border-color);
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.popular-item:hover {
    border-color: var(--n-primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.popular-code {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 4px;
}

.popular-name {
    font-size: 14px;
    color: var(--n-text-color-2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-page {
        padding: 16px;
    }
    
    .suggestion-list {
        grid-template-columns: 1fr;
    }
    
    .popular-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

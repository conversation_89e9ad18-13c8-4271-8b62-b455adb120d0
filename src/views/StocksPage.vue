<template>
    <div class="stocks-page">
        <div class="page-header">
            <div class="header-content">
                <h2 class="page-title">股票市场</h2>
                <p class="page-description">浏览和筛选股票，添加到投资组合</p>
            </div>
            
            <div class="header-actions">
                <NSelect
                    v-model:value="selectedMarket"
                    :options="marketOptions"
                    placeholder="选择市场"
                    style="width: 150px;"
                />
                <NButton 
                    v-if="selectedStocks.length > 0"
                    type="primary"
                    @click="showAddToPortfolioModal = true"
                >
                    添加到组合 ({{ selectedStocks.length }})
                </NButton>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="filters-section">
            <div class="search-filter">
                <NInput
                    v-model:value="searchQuery"
                    placeholder="搜索股票代码或名称..."
                    clearable
                >
                    <template #prefix>
                        <NIcon>
                            <Search />
                        </NIcon>
                    </template>
                </NInput>
            </div>
            
            <div class="advanced-filters">
                <NSelect
                    v-model:value="selectedIndustry"
                    :options="industryOptions"
                    placeholder="行业筛选"
                    clearable
                    style="width: 200px;"
                />
                <NSelect
                    v-model:value="sortBy"
                    :options="sortOptions"
                    placeholder="排序方式"
                    style="width: 150px;"
                />
            </div>
        </div>

        <!-- 股票表格 -->
        <div class="table-section">
            <NDataTable
                :columns="columns"
                :data="filteredStocks"
                :loading="loading"
                :pagination="pagination"
                :row-key="(row: Stock) => row.code"
                :checked-row-keys="selectedStocks"
                @update:checked-row-keys="handleSelectionChange"
                size="small"
                striped
                class="stocks-table"
            />
        </div>

        <!-- 添加到投资组合模态框 -->
        <NModal 
            v-model:show="showAddToPortfolioModal" 
            preset="card" 
            title="添加到投资组合" 
            style="width: 500px;"
        >
            <div class="add-to-portfolio-content">
                <div class="selected-stocks-info">
                    <h4>已选择 {{ selectedStocks.length }} 只股票</h4>
                    <div class="selected-stocks-list">
                        <NTag 
                            v-for="stockCode in selectedStocks.slice(0, 10)" 
                            :key="stockCode"
                            size="small"
                            class="selected-stock-tag"
                        >
                            {{ stockCode }}
                        </NTag>
                        <span v-if="selectedStocks.length > 10" class="more-selected">
                            +{{ selectedStocks.length - 10 }}
                        </span>
                    </div>
                </div>

                <NDivider />

                <div class="portfolio-selection">
                    <h4>选择投资组合</h4>
                    <NRadioGroup v-model:value="targetPortfolioId">
                        <div class="portfolio-options">
                            <NRadio 
                                v-for="portfolio in availablePortfolios" 
                                :key="portfolio.id"
                                :value="portfolio.id"
                                class="portfolio-option"
                            >
                                <div class="portfolio-option-content">
                                    <span class="portfolio-name">{{ portfolio.name }}</span>
                                    <span class="portfolio-stocks-count">{{ portfolio.stocks.length }} 只股票</span>
                                </div>
                            </NRadio>
                            <NRadio value="new" class="portfolio-option">
                                <div class="portfolio-option-content">
                                    <span class="portfolio-name">创建新组合</span>
                                    <span class="portfolio-stocks-count">新建投资组合</span>
                                </div>
                            </NRadio>
                        </div>
                    </NRadioGroup>

                    <div v-if="targetPortfolioId === 'new'" class="new-portfolio-input">
                        <NInput
                            v-model:value="newPortfolioName"
                            placeholder="输入新组合名称..."
                        />
                    </div>
                </div>

                <div class="modal-actions">
                    <NButton @click="showAddToPortfolioModal = false">取消</NButton>
                    <NButton 
                        type="primary" 
                        @click="addToPortfolio"
                        :disabled="!targetPortfolioId || (targetPortfolioId === 'new' && !newPortfolioName.trim())"
                    >
                        添加
                    </NButton>
                </div>
            </div>
        </NModal>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, h } from 'vue';
import { 
    NInput,
    NSelect,
    NButton,
    NIcon,
    NDataTable,
    NTag,
    NModal,
    NDivider,
    NRadioGroup,
    NRadio,
    useMessage
} from 'naive-ui';
import { Search } from '@vicons/ionicons5';
import { invoke } from '@tauri-apps/api/core';

const message = useMessage();

// 股票数据类型
interface Stock {
    code: string;
    name: string;
    exchangeCode: string;
    countryCode: string;
    type: string;
    industry?: string;
    price?: number;
    change?: number;
    changePercent?: number;
}

// 投资组合类型
interface Portfolio {
    id: string;
    name: string;
    stocks: string[];
}

// 页面状态
const loading = ref(false);
const stocks = ref<Stock[]>([]);
const selectedStocks = ref<string[]>([]);
const showAddToPortfolioModal = ref(false);

// 筛选状态
const searchQuery = ref('');
const selectedMarket = ref('A股');
const selectedIndustry = ref('');
const sortBy = ref('code');

// 投资组合相关
const availablePortfolios = ref<Portfolio[]>([]);
const targetPortfolioId = ref('');
const newPortfolioName = ref('');

// 市场选项
const marketOptions = [
    { label: 'A股市场', value: 'A股' },
    { label: '港股市场', value: '港股' },
    { label: '美股市场', value: '美股' }
];

// 行业选项
const industryOptions = [
    { label: '暂无', value: '暂无' }
];

// 排序选项
const sortOptions = [
    { label: '股票代码', value: 'code' },
    { label: '股票名称', value: 'name' }
];

// 表格列定义
const columns = [
    {
        type: 'selection' as const
    },
    {
        title: '股票代码',
        key: 'code',
        width: 120,
        sorter: (a: Stock, b: Stock) => a.code.localeCompare(b.code)
    },
    {
        title: '股票名称',
        key: 'name',
        width: 150,
        sorter: (a: Stock, b: Stock) => a.name.localeCompare(b.name)
    },
    {
        title: '市场',
        key: 'exchangeCode',
        width: 100,
        render: (row: Stock) => {
            const marketMap: Record<string, string> = {
                'SZ': '深交所',
                'SH': '上交所',
                'BJ': '北交所'
            };
            return marketMap[row.exchangeCode] || row.exchangeCode;
        }
    },
    {
        title: '行业',
        key: 'industry',
        width: 120,
        render: (row: Stock) => row.industry || '暂无'
    },
    {
        title: '现价',
        key: 'price',
        width: 100,
        render: () => '暂无'
    },
    {
        title: '涨跌幅',
        key: 'changePercent',
        width: 100,
        render: () => '暂无'
    },
    {
        title: '操作',
        key: 'actions',
        width: 120,
        render: (row: Stock) => {
            return h(NButton, {
                size: 'small',
                quaternary: true,
                onClick: () => viewStockDetail(row.code)
            }, { default: () => '查看详情' });
        }
    }
];

// 分页配置
const pagination = {
    pageSize: 50,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    showQuickJumper: true
};

// 筛选后的股票数据
const filteredStocks = computed(() => {
    let result = stocks.value;

    // 搜索筛选
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(stock => 
            stock.code.toLowerCase().includes(query) ||
            stock.name.toLowerCase().includes(query)
        );
    }

    // 行业筛选
    if (selectedIndustry.value) {
        result = result.filter(stock => stock.industry === selectedIndustry.value);
    }

    // 排序
    if (sortBy.value) {
        result = [...result].sort((a, b) => {
            const aVal = a[sortBy.value as keyof Stock];
            const bVal = b[sortBy.value as keyof Stock];
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
                return aVal.localeCompare(bVal);
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
                return bVal - aVal; // 数字降序
            }
            return 0;
        });
    }

    return result;
});

// 处理选择变化
const handleSelectionChange = (keys: (string | number)[]) => {
    selectedStocks.value = keys.map(key => String(key));
};

// 查看股票详情
const viewStockDetail = (code: string) => {
    // 这里可以跳转到股票详情页或打开详情模态框
    message.info(`查看股票 ${code} 的详情`);
};

// 添加到投资组合
const addToPortfolio = () => {
    if (targetPortfolioId.value === 'new') {
        // 创建新投资组合
        const newPortfolio: Portfolio = {
            id: Date.now().toString(),
            name: newPortfolioName.value.trim(),
            stocks: [...selectedStocks.value]
        };
        availablePortfolios.value.push(newPortfolio);
        savePortfolios();
        message.success(`已创建新组合"${newPortfolio.name}"并添加了 ${selectedStocks.value.length} 只股票`);
    } else {
        // 添加到现有投资组合
        const portfolio = availablePortfolios.value.find(p => p.id === targetPortfolioId.value);
        if (portfolio) {
            const newStocks = selectedStocks.value.filter(stock => !portfolio.stocks.includes(stock));
            portfolio.stocks.push(...newStocks);
            savePortfolios();
            message.success(`已向"${portfolio.name}"添加了 ${newStocks.length} 只新股票`);
        }
    }

    // 重置状态
    selectedStocks.value = [];
    showAddToPortfolioModal.value = false;
    targetPortfolioId.value = '';
    newPortfolioName.value = '';
};

// 加载股票数据
const loadStocks = async () => {
    loading.value = true;
    try {
        const stockList = await invoke('stock_list');
        stocks.value = (stockList as Stock[]).map(stock => ({
            ...stock,
            industry: '暂无',
            price: undefined,
            changePercent: undefined
        }));
    } catch (error) {
        message.error('加载股票数据失败');
        console.error('Failed to load stocks:', error);
    } finally {
        loading.value = false;
    }
};

// 加载投资组合
const loadPortfolios = () => {
    const saved = localStorage.getItem('portfolios');
    if (saved) {
        availablePortfolios.value = JSON.parse(saved);
    }
};

// 保存投资组合
const savePortfolios = () => {
    localStorage.setItem('portfolios', JSON.stringify(availablePortfolios.value));
};

// 初始化
onMounted(() => {
    loadStocks();
    loadPortfolios();
});
</script>

<style scoped>
.stocks-page {
    padding: 24px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
}

.header-content h2 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
}

.header-content p {
    margin: 0;
    color: var(--n-text-color-2);
    font-size: 16px;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filters-section {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.search-filter {
    flex: 1;
    min-width: 300px;
}

.advanced-filters {
    display: flex;
    gap: 12px;
}

.table-section {
    background: var(--n-card-color);
    border-radius: 8px;
    overflow: hidden;
}

.stocks-table {
    min-height: 400px;
}

.add-to-portfolio-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.selected-stocks-info h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
}

.selected-stocks-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.selected-stock-tag {
    font-size: 12px;
}

.more-selected {
    font-size: 12px;
    color: var(--n-text-color-2);
}

.portfolio-selection h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
}

.portfolio-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.portfolio-option {
    width: 100%;
}

.portfolio-option-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.portfolio-name {
    font-weight: 500;
}

.portfolio-stocks-count {
    font-size: 12px;
    color: var(--n-text-color-2);
}

.new-portfolio-input {
    margin-top: 12px;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stocks-page {
        padding: 16px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .filters-section {
        flex-direction: column;
    }
    
    .advanced-filters {
        flex-wrap: wrap;
    }
}
</style>

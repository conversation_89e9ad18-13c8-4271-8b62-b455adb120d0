import { createRouter, createWebHashHistory } from "vue-router";
import type { RouteRecordRaw } from "vue-router";
import SearchPage from "./views/SearchPage.vue";
import PortfolioPage from "./views/PortfolioPage.vue";
import StocksPage from "./views/StocksPage.vue";
import SettingsPage from "./views/SettingsPage.vue";
import StockPage from "./StockPage.vue";

const routes: RouteRecordRaw[] = [
  { path: "/", component: SearchPage },
  { path: "/portfolio", component: PortfolioPage },
  { path: "/stocks", component: StocksPage },
  { path: "/settings", component: SettingsPage },
  { path: "/stock/:codeOrName", component: StockPage },
  // 重定向旧路由
  { path: "/index", redirect: "/" },
];

export const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// 定义支持的语言类型
export type SupportedLanguage = 'zh-CN' | 'en-US';

// 定义翻译文本的类型
export interface Translations {
    // 通用
    common: {
        confirm: string;
        cancel: string;
        save: string;
        delete: string;
        edit: string;
        add: string;
        loading: string;
        success: string;
        error: string;
        warning: string;
        info: string;
    };
    
    // 导航
    nav: {
        stocks: string;
        portfolio: string;
        settings: string;
        search: string;
    };
    
    // 设置页面
    settings: {
        title: string;
        general: string;
        theme: string;
        themeDescription: string;
        language: string;
        languageDescription: string;
        sidebar: string;
        sidebarDescription: string;
        autoSave: string;
        autoSaveDescription: string;
        cacheTime: string;
        cacheTimeDescription: string;
        darkTheme: string;
        lightTheme: string;
        autoTheme: string;
        chinese: string;
        english: string;
    };
}

// 中文翻译
const zhCN: Translations = {
    common: {
        confirm: '确认',
        cancel: '取消',
        save: '保存',
        delete: '删除',
        edit: '编辑',
        add: '添加',
        loading: '加载中...',
        success: '成功',
        error: '错误',
        warning: '警告',
        info: '信息'
    },
    nav: {
        stocks: '股票',
        portfolio: '投资组合',
        settings: '设置',
        search: '搜索'
    },
    settings: {
        title: '设置',
        general: '常规设置',
        theme: '主题模式',
        themeDescription: '选择应用的主题外观',
        language: '语言',
        languageDescription: '选择界面显示语言',
        sidebar: '侧边栏默认状态',
        sidebarDescription: '设置侧边栏的默认展开状态',
        autoSave: '自动保存',
        autoSaveDescription: '自动保存投资组合和设置',
        cacheTime: '数据缓存时间',
        cacheTimeDescription: '股票数据的缓存有效期（分钟）',
        darkTheme: '深色主题',
        lightTheme: '浅色主题',
        autoTheme: '跟随系统',
        chinese: '简体中文',
        english: 'English'
    }
};

// 英文翻译
const enUS: Translations = {
    common: {
        confirm: 'Confirm',
        cancel: 'Cancel',
        save: 'Save',
        delete: 'Delete',
        edit: 'Edit',
        add: 'Add',
        loading: 'Loading...',
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        info: 'Info'
    },
    nav: {
        stocks: 'Stocks',
        portfolio: 'Portfolio',
        settings: 'Settings',
        search: 'Search'
    },
    settings: {
        title: 'Settings',
        general: 'General Settings',
        theme: 'Theme Mode',
        themeDescription: 'Choose the theme appearance of the application',
        language: 'Language',
        languageDescription: 'Select the interface display language',
        sidebar: 'Default Sidebar State',
        sidebarDescription: 'Set the default expanded state of the sidebar',
        autoSave: 'Auto Save',
        autoSaveDescription: 'Automatically save portfolios and settings',
        cacheTime: 'Data Cache Time',
        cacheTimeDescription: 'Cache validity period for stock data (minutes)',
        darkTheme: 'Dark Theme',
        lightTheme: 'Light Theme',
        autoTheme: 'Follow System',
        chinese: '简体中文',
        english: 'English'
    }
};

// 翻译映射
const translations: Record<SupportedLanguage, Translations> = {
    'zh-CN': zhCN,
    'en-US': enUS
};

export const useI18nStore = defineStore('i18n', () => {
    // 当前语言 - 从 localStorage 直接读取，避免循环依赖
    const getInitialLanguage = (): SupportedLanguage => {
        try {
            const saved = localStorage.getItem('app-settings');
            if (saved) {
                const settings = JSON.parse(saved);
                return settings.language || 'zh-CN';
            }
        } catch (error) {
            console.error('Failed to load language from settings:', error);
        }
        return 'zh-CN';
    };

    const initialLang = getInitialLanguage();
    console.log('I18n store: Initial language:', initialLang);
    const currentLanguage = ref<SupportedLanguage>(initialLang);

    // 当前翻译
    const t = computed(() => translations[currentLanguage.value]);

    // 设置语言
    const setLanguage = (language: SupportedLanguage) => {
        console.log('I18n store: setLanguage called with:', language);
        currentLanguage.value = language;
    };

    // 监听语言变化事件
    const syncWithSettings = () => {
        if (typeof window !== 'undefined') {
            // 监听自定义语言变化事件
            const handleLanguageChange = (event: CustomEvent) => {
                const newLanguage = event.detail.language;
                console.log('I18n: Received language change event:', newLanguage);
                if (newLanguage !== currentLanguage.value) {
                    currentLanguage.value = newLanguage;
                }
            };

            window.addEventListener('language-changed', handleLanguageChange as EventListener);

            // 初始同步
            const initialLanguage = getInitialLanguage();
            if (initialLanguage !== currentLanguage.value) {
                console.log('I18n: Initial sync from', currentLanguage.value, 'to', initialLanguage);
                currentLanguage.value = initialLanguage;
            }

            // 返回清理函数
            return () => {
                window.removeEventListener('language-changed', handleLanguageChange as EventListener);
            };
        }

        return () => {};
    };
    
    // 获取翻译文本的辅助函数
    const getText = (path: string): string => {
        const keys = path.split('.');
        let result: any = t.value;
        
        for (const key of keys) {
            if (result && typeof result === 'object' && key in result) {
                result = result[key];
            } else {
                return path; // 如果找不到翻译，返回原始路径
            }
        }
        
        return typeof result === 'string' ? result : path;
    };
    
    // 初始化同步
    syncWithSettings();

    return {
        currentLanguage,
        t,
        setLanguage,
        getText,
        syncWithSettings
    };
});

<template>
    <div class="page-container">
        <!-- 错误状态 -->
        <div v-if="hasError" class="error-container">
            <h1 class="error-title">加载失败</h1>
            <p class="error-message">{{ errorMessage }}</p>
            <NButton @click="$router.go(-1)" type="primary">返回</NButton>
        </div>

        <!-- 正常内容 -->
        <template v-else>
            <div class="header">
                <h1 class="stock-name">{{ stockData?.name || "加载中..." }}</h1>
                <p class="stock-code">{{ stockData?.code }}</p>
            </div>

            <div class="data-grid">
                <NCard class="data-card" title="年化收益率">
                    <div
                        class="data-value"
                        :class="
                            getColorClass(stockData?.annualized_profit_rate)
                        "
                    >
                        {{ formatPercent(stockData?.annualized_profit_rate) }}
                    </div>
                    <div class="data-subtitle">Annualized Return</div>
                </NCard>

                <NCard class="data-card" title="标准差">
                    <div class="data-value">
                        {{ formatPercent(stockData?.standard_deviation) }}
                    </div>
                    <div class="data-subtitle">Standard Deviation</div>
                </NCard>

                <NCard class="data-card" title="β 系数 (Beta)">
                    <div class="data-value">
                        {{ stockData?.beta?.toFixed(4) }}
                    </div>
                    <div class="data-subtitle">Systematic Risk</div>
                </NCard>

                <NCard class="data-card" title="CAPM 预期收益率">
                    <div class="data-value">
                        {{ formatPercent(stockData?.capm) }}
                    </div>
                    <div class="data-subtitle">Expected Return (CAPM)</div>
                </NCard>
            </div>
        </template>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { invoke } from "@tauri-apps/api/core";
import { NCard, NButton, useLoadingBar, useMessage } from "naive-ui";
import { markRaw } from "vue";

const loadingBar = useLoadingBar();
const message = useMessage();
const route = useRoute();

type HistoricStockQuote = {
    day: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
};

type AssetPrice = {
    date: string;
    price: number;
};

type StockData = {
    name: string;
    code: string;
    annualized_profit_rate: number;
    standard_deviation: number;
    historic_stock_quote: HistoricStockQuote[];
    forward_adjusted_prices: AssetPrice[];
    beta: number;
    capm: number;
};

const stockData = ref<StockData | null>(null);
const hasError = ref(false);
const errorMessage = ref("");
const formatPercent = (v?: number) =>
    v !== undefined ? (v * 100).toFixed(2) + "%" : "--";

const getColorClass = (v?: number) => {
    if (v === undefined) return "";
    if (v > 0.05) return "positive";
    if (v < 0) return "negative";
    return "neutral";
};

onMounted(async () => {
    loadingBar.start();
    try {
        const result: StockData = await invoke("stock_page_data", {
            codeOrName: route.params.codeOrName,
        });
        stockData.value = markRaw(result);
        loadingBar.finish();
    } catch (error) {
        loadingBar.error();
        hasError.value = true;
        errorMessage.value = String(error);
        console.error("Failed to load stock data:", error);
        message.error(`加载股票数据失败: ${error}`);
    }
});
</script>

<style scoped>
.page-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 5vw;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.stock-name {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
}

.stock-code {
    font-size: 1.1rem;
    color: #888;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    width: 100%;
    max-width: 1100px;
}

.data-card {
    text-align: center;
    border-radius: 16px;
    transition: all 0.2s ease;
}

.data-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 14px rgba(0, 0, 0, 0.08);
}

.data-value {
    font-size: 2rem;
    font-weight: 700;
    margin-top: 10px;
}

.data-subtitle {
    font-size: 0.9rem;
    color: #888;
    margin-top: 6px;
}

.positive {
    color: #2ecc71;
}

.negative {
    color: #e74c3c;
}

.neutral {
    color: #999;
}

.error-container {
    text-align: center;
    padding: 60px 20px;
}

.error-title {
    font-size: 2rem;
    font-weight: 700;
    color: #e74c3c;
    margin-bottom: 16px;
}

.error-message {
    font-size: 1.1rem;
    color: #888;
    margin-bottom: 24px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}
</style>

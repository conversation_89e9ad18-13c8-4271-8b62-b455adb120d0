<template>
    <div class="bottom-container">
        <div :style="bottomStyle">{{ bottomText }}</div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";

import { ref } from "vue";
import { listen } from "@tauri-apps/api/event";

const bottomText = ref("");

enum LogLevel {
    Debug = "Debug",
    Info = "Info",
    Warning = "Warning",
    Error = "Error",
    Critical = "Critical",
}

type Record = {
    file: string;
    level: LogLevel;
    line: number;
    message: string;
    time: string;
};

const bottomStyle = ref("font-size: 15px; color: #999");
const normalStyle = "font-size: 15px; color: #999";
const warningStyle = "font-size: 15px; color: #999900";
const errorStyle = "font-size: 15px; color: #ee2222";
onMounted(() => {
    listen<Record>("bottom-text", (event) => {
        const record = event.payload;
        if (record.level === LogLevel.Debug || record.level === LogLevel.Info) {
            bottomStyle.value = normalStyle;
        } else if (record.level === LogLevel.Warning) {
            bottomStyle.value = warningStyle;
        } else if (
            record.level === LogLevel.Error ||
            record.level === LogLevel.Critical
        ) {
            bottomStyle.value = errorStyle;
        }

        bottomText.value = record.message;
    });
});
</script>

<style scoped>
.bottom-container {
    display: flex;
    justify-content: center;
    width: 100%;
    position: fixed;
    bottom: 20px;
    left: 0;
}
</style>

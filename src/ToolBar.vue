<template>
    <div class="toolbar-container">
        <div class="toolbar-left">
            <NIcon @click="home">
                <Home class="icon" />
            </NIcon>
        </div>
        <div class="toolbar-right">
            <NIcon>
                <Settings class="icon" />
            </NIcon>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { Settings, Home } from "@vicons/ionicons5";
import { NIcon } from "naive-ui";
import { useRouter } from "vue-router";

const router = useRouter();
function home() {
    router.push("/");
}
</script>

<style scoped>
.toolbar-container {
    display: flex;
    justify-content: space-between;
    margin: 20px;
}

.toolbar-right {
    translate: -15px;
}
.icon {
    font-size: 30px;
    cursor: pointer;
    transition: 0.2s;
}

.icon:hover {
    color: #63e2b7;
    transform: scale(1.2);
    translate: 0px -10px;
}
</style>

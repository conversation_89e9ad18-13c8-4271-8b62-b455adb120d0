import { invoke } from '@tauri-apps/api/core';

// 测试投资组合优化功能
export async function testPortfolioOptimization() {
    console.log('开始测试投资组合优化功能...');
    
    const testStocks = ['002351', '002670']; // 测试用的股票代码
    
    try {
        // 测试最小方差优化
        console.log('测试最小方差优化...');
        const minVarianceResult = await invoke('optimize_min_variance', {
            request: {
                stock_codes: testStocks,
                window_days: 365,
                max_iter: 1000,
                profit_type: 'Logarithm'
            }
        });
        console.log('最小方差优化结果:', minVarianceResult);
        
        // 测试给定收益下最小方差优化
        console.log('测试给定收益下最小方差优化...');
        const minVarianceGivenReturnResult = await invoke('optimize_min_variance_given_return', {
            request: {
                stock_codes: testStocks,
                window_days: 365,
                target_return: 0.08,
                max_iter: 1000,
                profit_type: 'Logarithm'
            }
        });
        console.log('给定收益下最小方差优化结果:', minVarianceGivenReturnResult);
        
        // 测试给定方差下最大收益优化
        console.log('测试给定方差下最大收益优化...');
        const maxReturnGivenVarianceResult = await invoke('optimize_max_return_given_variance', {
            request: {
                stock_codes: testStocks,
                window_days: 365,
                target_variance: 0.15,
                max_iter: 1000,
                profit_type: 'Logarithm'
            }
        });
        console.log('给定方差下最大收益优化结果:', maxReturnGivenVarianceResult);
        
        console.log('所有优化测试完成！');
        return {
            minVariance: minVarianceResult,
            minVarianceGivenReturn: minVarianceGivenReturnResult,
            maxReturnGivenVariance: maxReturnGivenVarianceResult
        };
        
    } catch (error) {
        console.error('优化测试失败:', error);
        throw error;
    }
}

// 验证优化结果的合理性
export function validateOptimizationResult(result: any, optimizationType: string) {
    console.log(`验证${optimizationType}优化结果...`);
    
    // 检查必要字段
    const requiredFields = ['asset_weights', 'expected_return', 'standard_deviation', 'optimization_type'];
    for (const field of requiredFields) {
        if (!(field in result)) {
            throw new Error(`优化结果缺少必要字段: ${field}`);
        }
    }
    
    // 检查权重总和是否接近1
    const totalWeight = result.asset_weights.reduce((sum: number, asset: any) => sum + asset.weight, 0);
    if (Math.abs(totalWeight - 1.0) > 0.01) {
        console.warn(`权重总和不等于1: ${totalWeight}`);
    }
    
    // 检查权重是否为非负数
    for (const asset of result.asset_weights) {
        if (asset.weight < 0) {
            console.warn(`发现负权重: ${asset.code} = ${asset.weight}`);
        }
    }
    
    // 检查收益率和风险是否合理
    if (result.expected_return < -1 || result.expected_return > 1) {
        console.warn(`预期收益率可能不合理: ${result.expected_return}`);
    }
    
    if (result.standard_deviation < 0 || result.standard_deviation > 2) {
        console.warn(`标准差可能不合理: ${result.standard_deviation}`);
    }
    
    console.log(`${optimizationType}优化结果验证通过`);
    return true;
}

// 格式化优化结果用于显示
export function formatOptimizationResult(result: any) {
    return {
        optimizationType: result.optimization_type,
        expectedReturn: (result.expected_return * 100).toFixed(2) + '%',
        standardDeviation: (result.standard_deviation * 100).toFixed(2) + '%',
        sharpeRatio: ((result.expected_return - 0.03) / result.standard_deviation).toFixed(3),
        weights: result.asset_weights.map((asset: any) => ({
            code: asset.code,
            weight: (asset.weight * 100).toFixed(2) + '%'
        }))
    };
}

// 比较不同优化策略的结果
export function compareOptimizationResults(results: any) {
    console.log('比较不同优化策略的结果:');
    
    const comparison = {
        minVariance: formatOptimizationResult(results.minVariance),
        minVarianceGivenReturn: formatOptimizationResult(results.minVarianceGivenReturn),
        maxReturnGivenVariance: formatOptimizationResult(results.maxReturnGivenVariance)
    };
    
    console.table(comparison);
    
    // 找出最佳夏普比率
    const sharpeRatios = [
        { strategy: '最小方差', ratio: parseFloat(comparison.minVariance.sharpeRatio) },
        { strategy: '给定收益下最小方差', ratio: parseFloat(comparison.minVarianceGivenReturn.sharpeRatio) },
        { strategy: '给定方差下最大收益', ratio: parseFloat(comparison.maxReturnGivenVariance.sharpeRatio) }
    ];
    
    const bestStrategy = sharpeRatios.reduce((best, current) => 
        current.ratio > best.ratio ? current : best
    );
    
    console.log(`最佳夏普比率策略: ${bestStrategy.strategy} (${bestStrategy.ratio})`);
    
    return comparison;
}

<template>
    <div class="page-container">
        <div>
            <NInputGroup>
                <NAutoComplete
                    class="search-bar"
                    size="large"
                    placeholder="搜索股票代码"
                    v-model:value="searchInput"
                    :options="options"
                />

                <NIcon size="30" id="search" @click="searchStock">
                    <Search />
                </NIcon>
            </NInputGroup>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { NInputGroup, NIcon, NAutoComplete } from "naive-ui";
import { Search } from "@vicons/ionicons5";
import { invoke } from "@tauri-apps/api/core";
import { ref } from "vue";
import { onMounted } from "vue";
import { watchEffect } from "vue";
import { useRouter } from "vue-router";
import { useNotification } from "naive-ui";
const notification = useNotification();
type Stock = {
    code: string;
    name: string;
    exchangeCode: string;
    countryCode: string;
    type: string;
};

type AutoCompleteOption = {
    label: string;
    value: string;
};

const router = useRouter();
const searchInput = ref("");
const rawOptions: AutoCompleteOption[] = [];
const options = ref<AutoCompleteOption[]>([]);

watchEffect(() => {
    const input = searchInput.value.trim();
    options.value =
        input === ""
            ? rawOptions
            : rawOptions.filter((x) => x.label.includes(input));
});

// const options: Ref<string[]> = ref([]);
onMounted(async () => {
    const stockList: Stock[] = await invoke("stock_list");
    for (const stock of stockList) {
        rawOptions.push({ label: stock.name, value: stock.code });
    }

    for (const stock of stockList) {
        rawOptions.push({ label: stock.code, value: stock.code });
    }
});

function searchStock() {
    const searchKeyword = searchInput.value.trim();
    if (searchKeyword === "") {
        notification.error({
            content: "股票代码或公司名不能为空",
            duration: 2000,
            keepAliveOnHover: true,
        });
        return;
    }
    router.push(`/stock/${searchKeyword}`);
}
</script>

<style scoped>
.page-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 90vh;
}
.search-bar {
    width: 30vw;
    font-size: 30px;
    border-radius: 20px;
}

#search {
    padding: 10px;
    margin-left: 10px;
    transition: 0.2s;
    border: 1px solid transparent;
    border-radius: 20px;
}

#search:hover {
    color: #63e2b7;
    cursor: pointer;
    border-color: #63e2b7;
    transform: scale(1.2);
    translate: 0px -10px;
}
</style>

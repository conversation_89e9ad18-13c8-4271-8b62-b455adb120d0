<template>
    <div class="portfolio-chart">
        <div class="chart-header">
            <h3 class="chart-title">{{ title }}</h3>
            <div class="chart-controls">
                <NButtonGroup>
                    <NButton 
                        :type="chartType === 'pie' ? 'primary' : 'default'"
                        @click="chartType = 'pie'"
                        size="small"
                    >
                        饼图
                    </NButton>
                    <NButton 
                        :type="chartType === 'bar' ? 'primary' : 'default'"
                        @click="chartType = 'bar'"
                        size="small"
                    >
                        柱状图
                    </NButton>
                </NButtonGroup>
            </div>
        </div>
        
        <div class="chart-container" ref="chartContainer">
            <!-- 饼图 -->
            <div v-if="chartType === 'pie'" class="pie-chart">
                <svg :width="chartSize" :height="chartSize" class="pie-svg">
                    <g :transform="`translate(${chartSize/2}, ${chartSize/2})`">
                        <path
                            v-for="(segment, index) in pieSegments"
                            :key="index"
                            :d="segment.path"
                            :fill="segment.color"
                            :stroke="'var(--n-card-color)'"
                            :stroke-width="2"
                            class="pie-segment"
                            @mouseenter="highlightSegment(index)"
                            @mouseleave="unhighlightSegment"
                        />
                        <template v-for="(segment, index) in pieSegments" :key="`label-${index}`">
                            <text
                                v-if="segment.percentage > 5"
                                :x="segment.labelX"
                                :y="segment.labelY"
                                :fill="'var(--n-text-color)'"
                                text-anchor="middle"
                                dominant-baseline="middle"
                                class="pie-label"
                            >
                                {{ segment.percentage.toFixed(1) }}%
                            </text>
                        </template>
                    </g>
                </svg>
            </div>
            
            <!-- 柱状图 -->
            <div v-else class="bar-chart">
                <div class="bar-container">
                    <div
                        v-for="(item, index) in chartData"
                        :key="index"
                        class="bar-item"
                    >
                        <div class="bar-wrapper">
                            <div 
                                class="bar"
                                :style="{
                                    height: `${item.percentage}%`,
                                    backgroundColor: colors[index % colors.length]
                                }"
                            ></div>
                        </div>
                        <div class="bar-label">
                            <div class="bar-code">{{ item.code }}</div>
                            <div class="bar-percentage">{{ item.percentage.toFixed(1) }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 图例 -->
        <div class="chart-legend">
            <div
                v-for="(item, index) in chartData"
                :key="index"
                class="legend-item"
                :class="{ highlighted: highlightedIndex === index }"
                @mouseenter="highlightSegment(index)"
                @mouseleave="unhighlightSegment"
            >
                <div 
                    class="legend-color"
                    :style="{ backgroundColor: colors[index % colors.length] }"
                ></div>
                <div class="legend-info">
                    <span class="legend-code">{{ item.code }}</span>
                    <span class="legend-weight">{{ (item.weight * 100).toFixed(2) }}%</span>
                </div>
            </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="chart-stats" v-if="showStats">
            <div class="stat-item">
                <span class="stat-label">总资产数量</span>
                <span class="stat-value">{{ chartData.length }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最大权重</span>
                <span class="stat-value">{{ (Math.max(...chartData.map(d => d.weight)) * 100).toFixed(2) }}%</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最小权重</span>
                <span class="stat-value">{{ (Math.min(...chartData.map(d => d.weight)) * 100).toFixed(2) }}%</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">权重标准差</span>
                <span class="stat-value">{{ calculateWeightStd().toFixed(4) }}</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { NButton, NButtonGroup } from 'naive-ui';

interface ChartDataItem {
    code: string;
    weight: number;
    percentage: number;
}

interface PieSegment {
    path: string;
    color: string;
    percentage: number;
    labelX: number;
    labelY: number;
}

const props = defineProps<{
    data: Array<{ code: string; weight: number }>;
    title?: string;
    showStats?: boolean;
}>();

const chartType = ref<'pie' | 'bar'>('pie');
const chartContainer = ref<HTMLElement>();
const chartSize = ref(300);
const highlightedIndex = ref<number | null>(null);

// 颜色方案
const colors = [
    '#63e2b7', '#70c0e8', '#ffd93d', '#ff6b6b', '#a78bfa',
    '#fb7185', '#34d399', '#60a5fa', '#f59e0b', '#ef4444',
    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316', '#ec4899'
];

// 处理后的图表数据
const chartData = computed<ChartDataItem[]>(() => {
    const total = props.data.reduce((sum, item) => sum + item.weight, 0);
    return props.data
        .map(item => ({
            code: item.code,
            weight: item.weight,
            percentage: (item.weight / total) * 100
        }))
        .sort((a, b) => b.weight - a.weight);
});

// 饼图分段
const pieSegments = computed<PieSegment[]>(() => {
    if (chartType.value !== 'pie') return [];
    
    const radius = chartSize.value / 2 - 20;
    const labelRadius = radius * 0.7;
    let currentAngle = -Math.PI / 2; // 从顶部开始
    
    return chartData.value.map((item, index) => {
        const angle = (item.percentage / 100) * 2 * Math.PI;
        const startAngle = currentAngle;
        const endAngle = currentAngle + angle;
        
        // 计算路径
        const x1 = Math.cos(startAngle) * radius;
        const y1 = Math.sin(startAngle) * radius;
        const x2 = Math.cos(endAngle) * radius;
        const y2 = Math.sin(endAngle) * radius;
        
        const largeArcFlag = angle > Math.PI ? 1 : 0;
        
        const path = [
            `M 0 0`,
            `L ${x1} ${y1}`,
            `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
            `Z`
        ].join(' ');
        
        // 计算标签位置
        const labelAngle = startAngle + angle / 2;
        const labelX = Math.cos(labelAngle) * labelRadius;
        const labelY = Math.sin(labelAngle) * labelRadius;
        
        currentAngle = endAngle;
        
        return {
            path,
            color: colors[index % colors.length],
            percentage: item.percentage,
            labelX,
            labelY
        };
    });
});

// 高亮分段
const highlightSegment = (index: number) => {
    highlightedIndex.value = index;
};

const unhighlightSegment = () => {
    highlightedIndex.value = null;
};

// 计算权重标准差
const calculateWeightStd = () => {
    const weights = chartData.value.map(d => d.weight);
    const mean = weights.reduce((sum, w) => sum + w, 0) / weights.length;
    const variance = weights.reduce((sum, w) => sum + Math.pow(w - mean, 2), 0) / weights.length;
    return Math.sqrt(variance);
};

// 响应式调整图表大小
const updateChartSize = () => {
    if (chartContainer.value) {
        const containerWidth = chartContainer.value.clientWidth;
        chartSize.value = Math.min(containerWidth - 40, 300);
    }
};

onMounted(() => {
    updateChartSize();
    window.addEventListener('resize', updateChartSize);
});

onUnmounted(() => {
    window.removeEventListener('resize', updateChartSize);
});
</script>

<style scoped>
.portfolio-chart {
    background: var(--n-card-color);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--n-border-color);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: var(--n-text-color);
}

.chart-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.pie-chart {
    display: flex;
    justify-content: center;
}

.pie-segment {
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.pie-segment:hover {
    opacity: 0.8;
}

.pie-label {
    font-size: 12px;
    font-weight: 600;
    pointer-events: none;
}

.bar-chart {
    width: 100%;
}

.bar-container {
    display: flex;
    align-items: end;
    justify-content: center;
    gap: 8px;
    height: 200px;
    padding: 0 20px;
}

.bar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 60px;
}

.bar-wrapper {
    height: 160px;
    width: 100%;
    display: flex;
    align-items: end;
    margin-bottom: 8px;
}

.bar {
    width: 100%;
    border-radius: 4px 4px 0 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.bar:hover {
    opacity: 0.8;
}

.bar-label {
    text-align: center;
}

.bar-code {
    font-size: 12px;
    font-weight: 600;
    color: var(--n-text-color);
    margin-bottom: 2px;
}

.bar-percentage {
    font-size: 11px;
    color: var(--n-text-color-2);
}

.chart-legend {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    margin-bottom: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.legend-item:hover,
.legend-item.highlighted {
    background-color: var(--n-hover-color);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
}

.legend-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.legend-code {
    font-size: 13px;
    font-weight: 600;
    color: var(--n-text-color);
}

.legend-weight {
    font-size: 12px;
    color: var(--n-text-color-2);
}

.chart-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--n-border-color);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 12px;
    color: var(--n-text-color-2);
    margin-bottom: 4px;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--n-text-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chart-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
    
    .bar-container {
        padding: 0 10px;
        gap: 4px;
    }
    
    .bar-item {
        max-width: 40px;
    }
    
    .chart-legend {
        grid-template-columns: 1fr;
    }
    
    .chart-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

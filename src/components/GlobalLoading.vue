<template>
    <Teleport to="body">
        <Transition name="loading-fade">
            <div v-if="isLoading" class="global-loading-overlay">
                <div class="loading-content">
                    <div class="loading-spinner">
                        <NSpin size="large" />
                    </div>
                    <div class="loading-text">
                        {{ loadingText }}
                    </div>
                    <div v-if="progress && progress > 0" class="loading-progress">
                        <NProgress 
                            type="line" 
                            :percentage="progress" 
                            :show-indicator="false"
                            status="info"
                        />
                        <span class="progress-text">{{ progress }}%</span>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<script lang="ts" setup>
import { NSpin, NProgress } from 'naive-ui';

defineProps<{
    isLoading: boolean;
    loadingText?: string;
    progress?: number;
}>();
</script>

<style scoped>
.global-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: var(--n-card-color);
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--n-border-color);
    min-width: 280px;
}

.loading-spinner {
    margin-bottom: 16px;
}

.loading-text {
    font-size: 16px;
    color: var(--n-text-color);
    margin-bottom: 16px;
    font-weight: 500;
}

.loading-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-text {
    font-size: 14px;
    color: var(--n-text-color-2);
}

/* 动画效果 */
.loading-fade-enter-active,
.loading-fade-leave-active {
    transition: all 0.3s ease;
}

.loading-fade-enter-from,
.loading-fade-leave-to {
    opacity: 0;
}

.loading-fade-enter-to,
.loading-fade-leave-from {
    opacity: 1;
}

.loading-content {
    transform: scale(1);
    transition: transform 0.3s ease;
}

.loading-fade-enter-from .loading-content,
.loading-fade-leave-to .loading-content {
    transform: scale(0.9);
}
</style>

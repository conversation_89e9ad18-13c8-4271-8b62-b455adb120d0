<template>
    <div class="optimization-result">
        <div class="result-header">
            <div class="result-title">
                <h3>{{ result.portfolioName }}</h3>
                <NTag
                    :type="getOptimizationTypeColor(result.optimizationType)"
                    size="small"
                >
                    {{ getOptimizationTypeLabel(result.optimizationType) }}
                </NTag>
            </div>
            <div class="result-summary">
                <div class="summary-item">
                    <span class="summary-label">预期收益率</span>
                    <span
                        class="summary-value"
                        :class="getReturnClass(result.expectedReturn)"
                    >
                        {{ formatPercent(result.expectedReturn) }}
                    </span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">风险水平 (标准差)</span>
                    <span class="summary-value">{{
                        formatPercent(result.risk)
                    }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">夏普比率</span>
                    <span class="summary-value">{{
                        calculateSharpeRatio().toFixed(3)
                    }}</span>
                </div>
            </div>
        </div>

        <NDivider />

        <!-- 权重分配 -->
        <div class="weights-section">
            <h4>资产权重分配</h4>
            <div class="weights-content">
                <!-- 权重表格 -->
                <div class="weights-table">
                    <NDataTable
                        :columns="weightsColumns"
                        :data="result.weights"
                        :pagination="false"
                        size="small"
                        striped
                    />
                </div>

                <!-- 权重饼图 -->
                <div class="weights-chart">
                    <PortfolioChart
                        :data="chartWeightsData"
                        title="权重分配可视化"
                        :show-stats="true"
                    />
                </div>
            </div>
        </div>

        <NDivider />

        <!-- 有效前沿散点图 -->
        <div class="efficient-frontier-section">
            <h4>有效前沿分析</h4>
            <div class="efficient-frontier-container">
                <div v-if="efficientFrontierLoading" class="loading-container">
                    <NSpin size="large" />
                    <p>正在生成有效前沿...</p>
                </div>
                <div v-else-if="efficientFrontierError" class="error-container">
                    <NAlert type="error" :title="efficientFrontierError" />
                    <NButton @click="loadEfficientFrontier" size="small" style="margin-top: 12px">
                        重新加载
                    </NButton>
                </div>
                <div v-else-if="efficientFrontierData.length > 0" class="scatter-chart">
                    <svg :width="chartWidth" :height="chartHeight" class="frontier-svg">
                        <!-- 坐标轴 -->
                        <g class="axes">
                            <!-- X轴 -->
                            <line
                                :x1="margin.left"
                                :y1="chartHeight - margin.bottom"
                                :x2="chartWidth - margin.right"
                                :y2="chartHeight - margin.bottom"
                                stroke="var(--n-border-color)"
                                stroke-width="1"
                            />
                            <!-- Y轴 -->
                            <line
                                :x1="margin.left"
                                :y1="margin.top"
                                :x2="margin.left"
                                :y2="chartHeight - margin.bottom"
                                stroke="var(--n-border-color)"
                                stroke-width="1"
                            />

                            <!-- X轴标签 -->
                            <text
                                :x="chartWidth / 2"
                                :y="chartHeight - 10"
                                text-anchor="middle"
                                fill="var(--n-text-color)"
                                class="axis-label"
                            >
                                标准差 (%)
                            </text>

                            <!-- Y轴标签 -->
                            <text
                                :x="15"
                                :y="chartHeight / 2"
                                text-anchor="middle"
                                fill="var(--n-text-color)"
                                class="axis-label"
                                transform="rotate(-90, 15, 200)"
                            >
                                收益率 (%)
                            </text>

                            <!-- X轴刻度 -->
                            <g v-for="tick in xTicks" :key="tick.value" class="tick">
                                <line
                                    :x1="tick.x"
                                    :y1="chartHeight - margin.bottom"
                                    :x2="tick.x"
                                    :y2="chartHeight - margin.bottom + 5"
                                    stroke="var(--n-border-color)"
                                />
                                <text
                                    :x="tick.x"
                                    :y="chartHeight - margin.bottom + 18"
                                    text-anchor="middle"
                                    fill="var(--n-text-color-3)"
                                    class="tick-label"
                                >
                                    {{ tick.label }}
                                </text>
                            </g>

                            <!-- Y轴刻度 -->
                            <g v-for="tick in yTicks" :key="tick.value" class="tick">
                                <line
                                    :x1="margin.left - 5"
                                    :y1="tick.y"
                                    :x2="margin.left"
                                    :y2="tick.y"
                                    stroke="var(--n-border-color)"
                                />
                                <text
                                    :x="margin.left - 10"
                                    :y="tick.y + 4"
                                    text-anchor="end"
                                    fill="var(--n-text-color-3)"
                                    class="tick-label"
                                >
                                    {{ tick.label }}
                                </text>
                            </g>
                        </g>

                        <!-- 散点 -->
                        <g class="scatter-points">
                            <circle
                                v-for="(point, index) in efficientFrontierData"
                                :key="index"
                                :cx="getX(point.standard_deviation)"
                                :cy="getY(point.return_rate)"
                                r="4"
                                fill="#63e2b7"
                                :stroke="frontierPointStrokeColor"
                                stroke-width="2"
                                class="frontier-point"
                                @mouseenter="showTooltip($event, point)"
                                @mouseleave="hideTooltip"
                            />
                        </g>

                        <!-- 当前组合点 -->
                        <circle
                            :cx="getX(result.risk)"
                            :cy="getY(result.expectedReturn)"
                            r="6"
                            fill="#f59e0b"
                            :stroke="frontierPointStrokeColor"
                            stroke-width="2"
                            class="current-portfolio-point"
                        />
                        <text
                            :x="getX(result.risk) + 10"
                            :y="getY(result.expectedReturn) - 10"
                            fill="var(--n-text-color)"
                            class="current-portfolio-label"
                        >
                            当前组合
                        </text>
                    </svg>

                    <!-- 工具提示 -->
                    <div
                        v-if="tooltip.show"
                        class="chart-tooltip"
                        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
                    >
                        <div>收益率: {{ formatPercent(tooltip.data.return_rate) }}</div>
                        <div>标准差: {{ formatPercent(tooltip.data.standard_deviation) }}</div>
                    </div>
                </div>
                <div v-else class="empty-chart">
                    <p>暂无有效前沿数据</p>
                    <NButton @click="loadEfficientFrontier" size="small">
                        生成有效前沿
                    </NButton>
                </div>
            </div>
        </div>

        <NDivider />

        <!-- 风险收益分析 -->
        <div class="analysis-section">
            <h4>风险收益分析</h4>
            <div class="analysis-grid">
                <NCard title="收益分析" size="small">
                    <div class="analysis-content">
                        <div class="metric-item">
                            <span class="metric-label">年化收益率</span>
                            <span class="metric-value">{{
                                formatPercent(result.expectedReturn)
                            }}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">月化收益率</span>
                            <span class="metric-value">{{
                                formatPercent(result.expectedReturn / 12)
                            }}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">收益等级</span>
                            <span class="metric-value">{{
                                getReturnLevel(result.expectedReturn)
                            }}</span>
                        </div>
                    </div>
                </NCard>

                <NCard title="风险分析" size="small">
                    <div class="analysis-content">
                        <div class="metric-item">
                            <span class="metric-label">年化波动率</span>
                            <span class="metric-value">{{
                                formatPercent(result.risk)
                            }}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">月化波动率</span>
                            <span class="metric-value">{{
                                formatPercent(result.risk / Math.sqrt(12))
                            }}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">风险等级</span>
                            <span class="metric-value">{{
                                getRiskLevel(result.risk)
                            }}</span>
                        </div>
                    </div>
                </NCard>

                <NCard title="综合评价" size="small">
                    <div class="analysis-content">
                        <div class="metric-item">
                            <span class="metric-label">夏普比率</span>
                            <span class="metric-value">{{
                                calculateSharpeRatio().toFixed(3)
                            }}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">风险调整收益</span>
                            <span class="metric-value">{{
                                (result.expectedReturn / result.risk).toFixed(3)
                            }}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">投资建议</span>
                            <span class="metric-value">{{
                                getInvestmentAdvice()
                            }}</span>
                        </div>
                    </div>
                </NCard>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="result-actions">
            <NButton @click="exportResult">导出结果</NButton>
            <NButton @click="saveToPortfolio" type="primary"
                >保存到组合</NButton
            >
            <NButton @click="$emit('close')" quaternary>关闭</NButton>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, ref, computed, onMounted } from "vue";
import {
    NTag,
    NDivider,
    NDataTable,
    NCard,
    NButton,
    NSpin,
    NAlert,
    useMessage,
} from "naive-ui";
import { invoke } from "@tauri-apps/api/core";
import PortfolioChart from "./PortfolioChart.vue";
import { useSettingsStore } from "../stores/settings";

// 股票数据接口
interface Stock {
    code: string;
    name: string;
    exchangeCode: string;
    countryCode: string;
    type: string;
}

const emit = defineEmits<{
    close: [];
}>();

const message = useMessage();
const settingsStore = useSettingsStore();

// 计算有效前沿点的边框颜色（根据主题动态调整）
const frontierPointStrokeColor = computed(() => {
    // 在深色主题下使用白色边框，在浅色主题下使用深色边框
    return settingsStore.settings.theme === 'light' ? '#333333' : '#ffffff';
});

// 计算坐标范围
const xExtent = computed(() => {
    if (efficientFrontierData.value.length === 0) return [0, 1];
    const values = efficientFrontierData.value.map(d => d.standard_deviation);
    values.push(props.result.risk); // 包含当前组合点
    const min = Math.min(...values);
    const max = Math.max(...values);
    const padding = (max - min) * 0.1;
    return [Math.max(0, min - padding), max + padding];
});

const yExtent = computed(() => {
    if (efficientFrontierData.value.length === 0) return [0, 1];
    const values = efficientFrontierData.value.map(d => d.return_rate);
    values.push(props.result.expectedReturn); // 包含当前组合点
    const min = Math.min(...values);
    const max = Math.max(...values);
    const padding = (max - min) * 0.1;
    return [min - padding, max + padding];
});

// 坐标转换函数
const getX = computed(() => (value: number) => {
    const [min, max] = xExtent.value;
    if (max === min) return margin.left + (chartWidth - margin.left - margin.right) / 2;
    return margin.left + ((value - min) / (max - min)) * (chartWidth - margin.left - margin.right);
});

const getY = computed(() => (value: number) => {
    const [min, max] = yExtent.value;
    if (max === min) return chartHeight - margin.bottom - (chartHeight - margin.top - margin.bottom) / 2;
    return chartHeight - margin.bottom - ((value - min) / (max - min)) * (chartHeight - margin.top - margin.bottom);
});

// X轴刻度
const xTicks = computed(() => {
    const [min, max] = xExtent.value;
    const tickCount = 5;
    if (max === min) {
        return [{
            value: min,
            x: getX.value(min),
            label: (min * 100).toFixed(1)
        }];
    }
    const step = (max - min) / (tickCount - 1);
    return Array.from({ length: tickCount }, (_, i) => {
        const value = min + i * step;
        return {
            value,
            x: getX.value(value),
            label: (value * 100).toFixed(1)
        };
    });
});

// Y轴刻度
const yTicks = computed(() => {
    const [min, max] = yExtent.value;
    const tickCount = 5;
    if (max === min) {
        return [{
            value: min,
            y: getY.value(min),
            label: (min * 100).toFixed(1)
        }];
    }
    const step = (max - min) / (tickCount - 1);
    return Array.from({ length: tickCount }, (_, i) => {
        const value = min + i * step;
        return {
            value,
            y: getY.value(value),
            label: (value * 100).toFixed(1)
        };
    });
});



// 接收优化结果数据
const props = defineProps<{
    result: {
        portfolioName: string;
        optimizationType: string;
        expectedReturn: number;
        risk: number;
        weights: Array<{
            code: string;
            weight: number;
        }>;
        // 添加用于生成有效前沿的参数
        stocks?: string[];
        windowDays?: number;
        maxIterations?: number;
        l2?: number;
        covariance_lambda?: number;
        profitType?: string;
    };
}>();

// 有效前沿数据点接口
interface EfficientFrontierDot {
    standard_deviation: number;
    return_rate: number;
}

// 有效前沿相关的响应式数据
const efficientFrontierData = ref<EfficientFrontierDot[]>([]);
const efficientFrontierLoading = ref(false);
const efficientFrontierError = ref<string>("");

// 股票名称映射
const stockCodeToName = ref<Map<string, string>>(new Map());

// 图表尺寸和边距
const chartWidth = 600;
const chartHeight = 400;
const margin = { top: 20, right: 40, bottom: 60, left: 60 };

// 工具提示
const tooltip = ref({
    show: false,
    x: 0,
    y: 0,
    data: {} as EfficientFrontierDot
});

// 加载有效前沿数据
const loadEfficientFrontier = async () => {
    if (!props.result.stocks || props.result.stocks.length === 0) {
        efficientFrontierError.value = "缺少股票代码信息，无法生成有效前沿";
        return;
    }

    efficientFrontierLoading.value = true;
    efficientFrontierError.value = "";

    try {
        const request = {
            codes: props.result.stocks,
            window_days: props.result.windowDays || 252,
            n_dots: 1000, // 生成20个点
            max_iter: props.result.maxIterations || 1000,
            l2: props.result.l2 || 0.0,
            covariance_lambda: props.result.covariance_lambda || 0.0,
            profit_type: props.result.profitType || "Logarithm"
        };

        console.log("Sending efficient frontier request:", request);
        const data = await invoke<EfficientFrontierDot[]>("draw_efficient_frontier", { request });
        console.log("Received efficient frontier data:", data);
        efficientFrontierData.value = data;

        if (data.length === 0) {
            efficientFrontierError.value = "未能生成有效前沿数据";
        }
    } catch (error) {
        console.error("Failed to load efficient frontier:", error);
        efficientFrontierError.value = `生成有效前沿失败: ${error}`;
    } finally {
        efficientFrontierLoading.value = false;
    }
};

// 显示工具提示
const showTooltip = (event: MouseEvent, data: EfficientFrontierDot) => {
    tooltip.value = {
        show: true,
        x: event.clientX + 10,
        y: event.clientY - 10,
        data
    };
};

// 隐藏工具提示
const hideTooltip = () => {
    tooltip.value.show = false;
};

// 加载股票名称映射
const loadStockNames = async () => {
    try {
        const stockList = await invoke<Stock[]>('stock_list');
        const mapping = new Map<string, string>();
        stockList.forEach(stock => {
            mapping.set(stock.code, stock.name);
        });
        stockCodeToName.value = mapping;
    } catch (error) {
        console.error('Failed to load stock names:', error);
    }
};

// 根据股票代码获取股票名称
const getStockName = (code: string): string => {
    return stockCodeToName.value.get(code) || code;
};

// 为图表准备的权重数据（使用股票名称）
const chartWeightsData = computed(() => {
    return props.result.weights.map(weight => ({
        code: getStockName(weight.code),
        weight: weight.weight
    }));
});

// 组件挂载时自动加载有效前沿和股票名称
onMounted(async () => {
    console.log("OptimizationResult mounted, props.result:", props.result);

    // 加载股票名称映射
    await loadStockNames();

    if (props.result.stocks && props.result.stocks.length > 0) {
        console.log("Loading efficient frontier for stocks:", props.result.stocks);
        loadEfficientFrontier();
    } else {
        console.warn("No stocks found in result, cannot load efficient frontier");
    }
});

// 权重表格列定义
const weightsColumns = [
    {
        title: "股票代码",
        key: "code",
        width: 120,
    },
    {
        title: "股票名称",
        key: "name",
        width: 150,
        render: (row: any) => getStockName(row.code),
    },
    {
        title: "权重",
        key: "weight",
        width: 100,
        render: (row: any) => formatPercent(row.weight),
    },
    {
        title: "权重条",
        key: "weightBar",
        render: (row: any) => {
            const percentage = (row.weight * 100).toFixed(1);
            return h(
                "div",
                {
                    style: {
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                    },
                },
                [
                    h(
                        "div",
                        {
                            style: {
                                width: "100px",
                                height: "8px",
                                backgroundColor: "var(--n-border-color)",
                                borderRadius: "4px",
                                overflow: "hidden",
                            },
                        },
                        [
                            h("div", {
                                style: {
                                    width: `${row.weight * 100}%`,
                                    height: "100%",
                                    backgroundColor: "#63e2b7",
                                    transition: "width 0.3s ease",
                                },
                            }),
                        ],
                    ),
                    h(
                        "span",
                        { style: { fontSize: "12px" } },
                        `${percentage}%`,
                    ),
                ],
            );
        },
    },
];

// 格式化百分比
const formatPercent = (value: number) => {
    return (value * 100).toFixed(2) + "%";
};

// 获取收益率颜色类
const getReturnClass = (value: number) => {
    if (value > 0.05) return "positive";
    if (value < 0) return "negative";
    return "neutral";
};

// 获取优化类型标签
const getOptimizationTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
        MinVariance: "最小风险",
        MinVarianceGivenReturn: "给定收益下最小风险",
        MaxReturnGivenVariance: "给定风险下最大收益",
        MaxSharpRatio: "最大化夏普率",
        RiskParitOptimization: "风险平价优化",
    };
    return labels[type] || type;
};

// 获取优化类型颜色
const getOptimizationTypeColor = (
    type: string,
): "default" | "error" | "primary" | "info" | "success" | "warning" => {
    const colors: Record<
        string,
        "default" | "error" | "primary" | "info" | "success" | "warning"
    > = {
        MinVariance: "info",
        MinVarianceGivenReturn: "success",
        MaxReturnGivenVariance: "warning",
    };
    return colors[type] || "default";
};

// 计算夏普比率（假设无风险利率为3%）
const calculateSharpeRatio = () => {
    const riskFreeRate = 0.03;
    return (props.result.expectedReturn - riskFreeRate) / props.result.risk;
};

// 获取收益等级
const getReturnLevel = (returnRate: number) => {
    if (returnRate > 0.15) return "高收益";
    if (returnRate > 0.08) return "中等收益";
    if (returnRate > 0.03) return "稳健收益";
    return "保守收益";
};

// 获取风险等级
const getRiskLevel = (risk: number) => {
    if (risk > 0.25) return "高风险";
    if (risk > 0.15) return "中等风险";
    if (risk > 0.08) return "低风险";
    return "极低风险";
};

// 获取投资建议
const getInvestmentAdvice = () => {
    const sharpeRatio = calculateSharpeRatio();
    if (sharpeRatio > 1.5) return "优秀";
    if (sharpeRatio > 1.0) return "良好";
    if (sharpeRatio > 0.5) return "一般";
    return "需要优化";
};

// 导出结果
const exportResult = () => {
    const data = {
        portfolioName: props.result.portfolioName,
        optimizationType: getOptimizationTypeLabel(
            props.result.optimizationType,
        ),
        expectedReturn: formatPercent(props.result.expectedReturn),
        risk: formatPercent(props.result.risk),
        sharpeRatio: calculateSharpeRatio().toFixed(3),
        weights: props.result.weights.map((w) => ({
            code: w.code,
            weight: formatPercent(w.weight),
        })),
        exportTime: new Date().toLocaleString(),
    };

    const jsonStr = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonStr], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${props.result.portfolioName}_优化结果.json`;
    a.click();
    URL.revokeObjectURL(url);

    message.success("优化结果已导出");
};

// 保存到投资组合
const saveToPortfolio = () => {
    // 这里可以实现保存逻辑
    message.success("优化结果已保存到投资组合");
    emit("close");
};
</script>

<style scoped>
.optimization-result {
    overflow-y: auto;
    padding: 20px;
}

.result-header {
    margin-bottom: 20px;
}

.result-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.result-title h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.result-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.summary-item {
    text-align: center;
    padding: 16px;
    background: var(--n-card-color);
    border-radius: 8px;
    border: 1px solid var(--n-border-color);
}

.summary-label {
    display: block;
    font-size: 12px;
    color: var(--n-text-color-2);
    margin-bottom: 8px;
}

.summary-value {
    display: block;
    font-size: 18px;
    font-weight: 600;
}

.summary-value.positive {
    color: #2ecc71;
}

.summary-value.negative {
    color: #e74c3c;
}

.summary-value.neutral {
    color: var(--n-text-color);
}

.weights-section h4,
.analysis-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
}

.weights-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 24px;
}

.weights-chart {
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    text-align: center;
    padding: 40px;
    border: 2px dashed var(--n-border-color);
    border-radius: 8px;
    width: 100%;
}

.chart-icon {
    color: var(--n-text-color-3);
    margin-bottom: 12px;
}

.chart-placeholder p {
    margin: 8px 0;
    color: var(--n-text-color-2);
}

.chart-note {
    font-size: 12px !important;
    color: var(--n-text-color-3) !important;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.analysis-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metric-label {
    font-size: 13px;
    color: var(--n-text-color-2);
}

.metric-value {
    font-size: 14px;
    font-weight: 500;
}

/* 有效前沿图表样式 */
.efficient-frontier-section {
    margin: 20px 0;
}

.efficient-frontier-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--n-text-color);
}

.efficient-frontier-container {
    position: relative;
    background: var(--n-card-color);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--n-border-color);
}

.loading-container,
.error-container,
.empty-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    text-align: center;
}

.loading-container p,
.empty-chart p {
    margin: 12px 0 0 0;
    color: var(--n-text-color-3);
}

.scatter-chart {
    position: relative;
    display: flex;
    justify-content: center;
}

.frontier-svg {
    background: var(--n-card-color);
    border-radius: 4px;
}

.axis-label {
    font-size: 12px;
    font-weight: 500;
}

.tick-label {
    font-size: 10px;
}



.frontier-point {
    cursor: pointer;
    transition: all 0.2s ease;
}

.frontier-point:hover {
    r: 6;
    filter: drop-shadow(0 2px 8px rgba(99, 226, 183, 0.5));
}

.current-portfolio-point {
    filter: drop-shadow(0 2px 8px rgba(245, 158, 11, 0.5));
    animation: pulse 2s infinite;
}

.current-portfolio-label {
    font-size: 12px;
    font-weight: 500;
    fill: var(--n-text-color);
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.chart-tooltip {
    position: fixed;
    background: var(--n-popover-color);
    border: 1px solid var(--n-border-color);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    pointer-events: none;
}

.chart-tooltip div {
    margin: 2px 0;
    color: var(--n-text-color);
}

.result-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--n-border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .result-summary {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .weights-content {
        grid-template-columns: 1fr;
    }

    .analysis-grid {
        grid-template-columns: 1fr;
    }

    .result-actions {
        flex-direction: column;
    }
}
</style>

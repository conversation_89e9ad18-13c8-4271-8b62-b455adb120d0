<template>
    <div class="error-boundary">
        <div v-if="hasError" class="error-content">
            <div class="error-icon">
                <NIcon size="64" color="#e88080">
                    <AlertCircle />
                </NIcon>
            </div>
            
            <h2 class="error-title">出现了一些问题</h2>
            
            <div class="error-message">
                <p>{{ errorMessage }}</p>
            </div>
            
            <div class="error-details" v-if="showDetails">
                <NCollapse>
                    <NCollapseItem title="错误详情" name="details">
                        <pre class="error-stack">{{ errorStack }}</pre>
                    </NCollapseItem>
                </NCollapse>
            </div>
            
            <div class="error-actions">
                <NButton @click="retry" type="primary">
                    <template #icon>
                        <NIcon>
                            <Refresh />
                        </NIcon>
                    </template>
                    重试
                </NButton>
                
                <NButton @click="goHome" secondary>
                    <template #icon>
                        <NIcon>
                            <Home />
                        </NIcon>
                    </template>
                    返回首页
                </NButton>
                
                <NButton @click="showDetails = !showDetails" text>
                    {{ showDetails ? '隐藏' : '显示' }}详情
                </NButton>
            </div>
            
            <div class="error-suggestions">
                <h3>可能的解决方案：</h3>
                <ul>
                    <li>检查网络连接是否正常</li>
                    <li>刷新页面重新加载</li>
                    <li>清除浏览器缓存</li>
                    <li>如果问题持续存在，请联系技术支持</li>
                </ul>
            </div>
        </div>
        
        <slot v-else />
    </div>
</template>

<script lang="ts" setup>
import { ref, onErrorCaptured } from 'vue';
import { useRouter } from 'vue-router';
import { 
    NIcon, 
    NButton, 
    NCollapse, 
    NCollapseItem,
    useMessage 
} from 'naive-ui';
import { 
    AlertCircle, 
    Refresh, 
    Home 
} from '@vicons/ionicons5';

const router = useRouter();
const message = useMessage();

const hasError = ref(false);
const errorMessage = ref('');
const errorStack = ref('');
const showDetails = ref(false);

// 捕获子组件错误
onErrorCaptured((error: Error, _instance, info) => {
    console.error('Error captured:', error, info);
    
    hasError.value = true;
    errorMessage.value = error.message || '未知错误';
    errorStack.value = error.stack || '';
    
    // 阻止错误继续传播
    return false;
});

// 重试操作
const retry = () => {
    hasError.value = false;
    errorMessage.value = '';
    errorStack.value = '';
    showDetails.value = false;
    
    // 重新加载当前路由
    router.go(0);
};

// 返回首页
const goHome = () => {
    hasError.value = false;
    router.push('/');
    message.info('已返回首页');
};

// 手动触发错误（用于测试）
const triggerError = (error: Error) => {
    hasError.value = true;
    errorMessage.value = error.message;
    errorStack.value = error.stack || '';
};

// 清除错误状态
const clearError = () => {
    hasError.value = false;
    errorMessage.value = '';
    errorStack.value = '';
    showDetails.value = false;
};

// 暴露方法给父组件
defineExpose({
    triggerError,
    clearError
});
</script>

<style scoped>
.error-boundary {
    min-height: 100%;
}

.error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 32px;
    text-align: center;
}

.error-icon {
    margin-bottom: 24px;
}

.error-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--n-text-color);
    margin: 0 0 16px 0;
}

.error-message {
    max-width: 600px;
    margin-bottom: 24px;
}

.error-message p {
    font-size: 16px;
    color: var(--n-text-color-2);
    line-height: 1.6;
    margin: 0;
}

.error-details {
    width: 100%;
    max-width: 800px;
    margin-bottom: 24px;
}

.error-stack {
    background: var(--n-code-color);
    border: 1px solid var(--n-border-color);
    border-radius: 6px;
    padding: 16px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: var(--n-text-color);
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

.error-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 32px;
    flex-wrap: wrap;
    justify-content: center;
}

.error-suggestions {
    max-width: 500px;
    text-align: left;
}

.error-suggestions h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--n-text-color);
    margin: 0 0 12px 0;
}

.error-suggestions ul {
    margin: 0;
    padding-left: 20px;
}

.error-suggestions li {
    font-size: 14px;
    color: var(--n-text-color-2);
    line-height: 1.6;
    margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .error-content {
        padding: 16px;
    }
    
    .error-title {
        font-size: 20px;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .n-button {
        width: 100%;
        max-width: 200px;
    }
}
</style>

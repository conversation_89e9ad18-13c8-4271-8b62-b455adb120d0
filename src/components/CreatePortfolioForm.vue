<template>
    <div class="create-portfolio-form">
        <NForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="top"
            size="medium"
        >
            <NFormItem label="组合名称" path="name">
                <NInput
                    v-model:value="formData.name"
                    placeholder="输入投资组合名称..."
                    maxlength="50"
                    show-count
                />
            </NFormItem>

            <NFormItem label="组合描述" path="description">
                <NInput
                    v-model:value="formData.description"
                    type="textarea"
                    placeholder="描述您的投资策略和目标..."
                    :rows="3"
                    maxlength="200"
                    show-count
                />
            </NFormItem>

            <NFormItem label="选择股票" path="stocks">
                <div class="stock-selection">
                    <NSelect
                        v-model:value="formData.stocks"
                        multiple
                        filterable
                        placeholder="搜索并选择股票..."
                        :options="stockOptions"
                        :loading="loadingStocks"
                        :max-tag-count="5"
                        size="medium"
                    />
                    <div class="stock-selection-hint">
                        <NIcon size="16">
                            <InformationCircle />
                        </NIcon>
                        <span>建议选择 3-20 只股票以实现有效分散投资</span>
                    </div>
                </div>
            </NFormItem>

            <NFormItem label="优化目标" path="optimizationGoal">
                <NRadioGroup v-model:value="formData.optimizationGoal">
                    <NSpace vertical>
                        <NRadio value="min_variance">
                            <div class="radio-option">
                                <div class="radio-title">最小风险</div>
                                <div class="radio-description">
                                    在不考虑收益约束的情况下，寻找风险最小的投资组合
                                </div>
                            </div>
                        </NRadio>
                        <NRadio value="min_variance_given_return">
                            <div class="radio-option">
                                <div class="radio-title">
                                    给定收益下最小风险
                                </div>
                                <div class="radio-description">
                                    在达到目标收益率的前提下，寻找风险最小的投资组合
                                </div>
                            </div>
                        </NRadio>
                        <NRadio value="max_return_given_variance">
                            <div class="radio-option">
                                <div class="radio-title">
                                    给定风险下最大收益
                                </div>
                                <div class="radio-description">
                                    在风险可控的范围内，寻找收益最大的投资组合
                                </div>
                            </div>
                        </NRadio>
                        <NRadio value="max_sharp_ratio">
                            <div class="radio-option">
                                <div class="radio-title">最大化夏普率</div>
                                <div class="radio-description">
                                    最大化每单位风险的收益
                                </div>
                            </div>
                        </NRadio>
                        <NRadio value="risk_parity">
                            <div class="radio-option">
                                <div class="radio-title">风险平价优化</div>
                                <div class="radio-description">
                                    尽可能减小每只股票对组合贡献的风险的差异，实现分散投资
                                </div>
                            </div>
                        </NRadio>
                    </NSpace>
                </NRadioGroup>
            </NFormItem>

            <!-- 目标收益率设置 -->
            <NFormItem
                v-if="formData.optimizationGoal === 'min_variance_given_return'"
                label="目标年化收益率"
                path="targetReturn"
            >
                <NInputNumber
                    v-model:value="formData.targetReturn"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    placeholder="0.08"
                    style="width: 100%"
                >
                    <template #suffix>%</template>
                </NInputNumber>
                <div class="input-hint">建议设置在 5%-20% 之间</div>
            </NFormItem>

            <!-- 目标风险水平设置 -->
            <NFormItem
                v-if="formData.optimizationGoal === 'max_return_given_variance'"
                label="目标风险水平"
                path="targetVariance"
            >
                <NInputNumber
                    v-model:value="formData.targetVariance"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    placeholder="0.15"
                    style="width: 100%"
                >
                    <template #suffix>%</template>
                </NInputNumber>
                <div class="input-hint">建议设置在 10%-30% 之间</div>
            </NFormItem>

            <NFormItem label="高级设置">
                <NCollapse>
                    <NCollapseItem title="优化参数" name="advanced">
                        <NSpace vertical>
                            <!-- 设置l2正则化防止过拟合 -->
                            <NFormItem label="l2 正则化" path="l2">
                                <NInputNumber
                                    v-model:value="formData.l2"
                                    :min="0"
                                    placeholder="20.0"
                                    style="width: 100%"
                                >
                                </NInputNumber>
                                <div class="input-hint">
                                    建议设置在 5 - 200 之间 （值越大投资越分散）
                                </div>
                            </NFormItem>

                            <!-- 设置协方差收缩系数防止过拟合 -->
                            <NFormItem
                                label="协方差收缩系数"
                                path="covariance_lambda"
                            >
                                <NInputNumber
                                    v-model:value="formData.covariance_lambda"
                                    :min="0"
                                    placeholder="0.2"
                                    style="width: 100%"
                                >
                                </NInputNumber>
                                <div class="input-hint">
                                    建议设置在 0.1 - 0.5 之间
                                </div>
                            </NFormItem>
                            <NFormItem label="时间窗口（天）" path="windowDays">
                                <NInputNumber
                                    v-model:value="formData.windowDays"
                                    :min="30"
                                    :max="1000"
                                    :step="1"
                                    placeholder="365"
                                    style="width: 100%"
                                />
                                <div class="input-hint">
                                    用于计算历史收益率和风险的时间窗口
                                </div>
                            </NFormItem>

                            <NFormItem
                                label="最大迭代次数"
                                path="maxIterations"
                            >
                                <NInputNumber
                                    v-model:value="formData.maxIterations"
                                    :min="100"
                                    :max="10000"
                                    :step="100"
                                    placeholder="1000"
                                    style="width: 100%"
                                />
                                <div class="input-hint">
                                    优化算法的最大迭代次数
                                </div>
                            </NFormItem>

                            <NFormItem label="收益率计算方式" path="profitType">
                                <NSelect
                                    v-model:value="formData.profitType"
                                    :options="profitTypeOptions"
                                    placeholder="选择计算方式"
                                />
                            </NFormItem>
                        </NSpace>
                    </NCollapseItem>
                </NCollapse>
            </NFormItem>
        </NForm>

        <div class="form-actions">
            <NButton @click="$emit('cancel')">取消</NButton>
            <NButton
                type="primary"
                @click="handleSubmit"
                :loading="submitting"
                :disabled="!isFormValid"
            >
                {{ isEditMode ? "更新组合" : "创建组合" }}
            </NButton>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import {
    NForm,
    NFormItem,
    NInput,
    NInputNumber,
    NSelect,
    NRadioGroup,
    NRadio,
    NButton,
    NSpace,
    NIcon,
    NCollapse,
    NCollapseItem,
    useMessage,
} from "naive-ui";
import { InformationCircle } from "@vicons/ionicons5";
import { invoke } from "@tauri-apps/api/core";

// Props定义
const props = defineProps<{
    initialData?: any;
}>();

const emit = defineEmits<{
    success: [portfolio: any];
    cancel: [];
}>();

const message = useMessage();

// 是否为编辑模式
const isEditMode = computed(() => !!props.initialData);

// 表单引用
const formRef = ref();

// 表单数据
const formData = ref({
    name: props.initialData?.name || "",
    description: props.initialData?.description || "",
    stocks: props.initialData?.stocks || ([] as string[]),
    optimizationGoal: props.initialData?.optimizationGoal || "min_variance",
    targetReturn: props.initialData?.targetReturn || 0.08,
    targetVariance: props.initialData?.targetVariance || 0.15,
    windowDays: props.initialData?.windowDays || 365,
    maxIterations: props.initialData?.maxIterations || 1000,
    profitType: props.initialData?.profitType || "Logarithm",
    l2: props.initialData?.l2 || 0.0,
    covariance_lambda: props.initialData?.covariance_lambda || 0.0,
});

// 表单状态
const submitting = ref(false);
const loadingStocks = ref(false);
const stockOptions = ref<Array<{ label: string; value: string }>>([]);

// 收益率计算方式选项
const profitTypeOptions = [
    { label: "对数收益率", value: "Logarithm" },
    { label: "简单收益率", value: "Arithmetic" },
];

// 表单验证规则
const rules = {
    name: [
        { required: true, message: "请输入组合名称", trigger: "blur" },
        {
            min: 2,
            message: "组合名称长度应在 2-50 个字符之间",
            trigger: "blur",
        },
    ],
    stocks: [
        {
            required: true,
            message: "请至少选择 2 只股票",
            trigger: "change",
            validator: (_rule: any, value: string[]) => {
                if (!value || value.length < 2) {
                    return new Error("请至少选择 2 只股票");
                }
                if (value.length > 50) {
                    return new Error("最多只能选择 50 只股票");
                }
                return true;
            },
        },
    ],
    targetReturn: [
        {
            required: true,
            message: "请输入目标收益率",
            trigger: "blur",
            validator: (_rule: any, value: number) => {
                if (value < 0 || value > 1) {
                    return new Error("目标收益率应在 0-100% 之间");
                }
                return true;
            },
        },
    ],
    targetVariance: [
        {
            required: true,
            message: "请输入目标风险水平",
            trigger: "blur",
            validator: (_rule: any, value: number) => {
                if (value < 0 || value > 1) {
                    return new Error("目标风险水平应在 0-100% 之间");
                }
                return true;
            },
        },
    ],
};

// 表单是否有效
const isFormValid = computed(() => {
    return (
        formData.value.name.trim() &&
        formData.value.stocks.length >= 2 &&
        formData.value.optimizationGoal
    );
});

// 加载股票选项
const loadStockOptions = async () => {
    loadingStocks.value = true;
    try {
        const stockList = (await invoke("stock_list")) as Array<{
            code: string;
            name: string;
        }>;

        stockOptions.value = stockList.map((stock) => ({
            label: `${stock.code} - ${stock.name}`,
            value: stock.code,
        }));
    } catch (error) {
        message.error("加载股票列表失败");
        console.error("Failed to load stock options:", error);
    } finally {
        loadingStocks.value = false;
    }
};

// 提交表单
const handleSubmit = async () => {
    try {
        await formRef.value?.validate();

        submitting.value = true;

        // 创建或更新投资组合对象
        const portfolio = {
            id: isEditMode.value ? props.initialData.id : Date.now().toString(),
            name: formData.value.name.trim(),
            description: formData.value.description.trim(),
            stocks: formData.value.stocks,
            optimizationGoal: formData.value.optimizationGoal,
            targetReturn: formData.value.targetReturn,
            targetVariance: formData.value.targetVariance,
            windowDays: formData.value.windowDays,
            maxIterations: formData.value.maxIterations,
            profitType: formData.value.profitType,
            covariance_lambda: formData.value.covariance_lambda,
            l2: formData.value.l2,
            expectedReturn: isEditMode.value
                ? props.initialData.expectedReturn
                : 0,
            risk: isEditMode.value ? props.initialData.risk : 0,
            createdAt: isEditMode.value
                ? props.initialData.createdAt
                : new Date(),
            optimizing: isEditMode.value ? props.initialData.optimizing : false,
        };

        emit("success", portfolio);
    } catch (error) {
        console.error("Form validation failed:", error);
    } finally {
        submitting.value = false;
    }
};

// 监听initialData变化，更新表单数据
watch(
    () => props.initialData,
    (newData) => {
        if (newData) {
            formData.value = {
                name: newData.name || "",
                description: newData.description || "",
                stocks: newData.stocks || [],
                optimizationGoal: newData.optimizationGoal || "min_variance",
                targetReturn: newData.targetReturn || 0.08,
                targetVariance: newData.targetVariance || 0.15,
                windowDays: newData.windowDays || 365,
                maxIterations: newData.maxIterations || 1000,
                profitType: newData.profitType || "Logarithm",
                covariance_lambda: newData.covariance_lambda || 0.0,
                l2: newData.l2 || 0.0,
            };
        }
    },
    { immediate: true },
);

// 初始化
onMounted(() => {
    loadStockOptions();
});
</script>

<style scoped>
.create-portfolio-form {
    max-width: 100%;
    overflow-y: auto;
}

.stock-selection {
    width: 100%;
}

.stock-selection-hint {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 12px;
    color: var(--n-text-color-2);
}

.radio-option {
    margin-left: 8px;
}

.radio-title {
    font-weight: 500;
    margin-bottom: 4px;
}

.radio-description {
    font-size: 12px;
    color: var(--n-text-color-2);
    line-height: 1.4;
}

.input-hint {
    font-size: 12px;
    color: var(--n-text-color-2);
    margin-top: 4px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--n-border-color);
}
</style>

{"name": "halloween-quant", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-fs": "~2.4.4", "@tauri-apps/plugin-opener": "^2", "@vicons/ionicons5": "^0.13.0", "pinia": "^3.0.4", "vue": "^3.5.13", "vue-router": "^4.6.3"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "naive-ui": "^2.43.1", "typescript": "~5.6.2", "vfonts": "^0.0.3", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}
use std::sync::{Arc, LazyLock};

use anyhow::Context;
use serde::{Deserialize, Serialize};
use tauri::{AppHandle, Runtime};
use tokio::sync::RwLock;

use crate::{
    data_source::{
        adjusted_share_price::{get_adjusted_share_price, AdjustDirection, AssetPrice},
        risk_free_rate::get_risk_free_rate,
        stock_quote::HistoricStockQuote,
    },
    indicators::{
        calculations::{annualized_return, MarketIndex, ProfitRateCalculationType},
        capm::{beta::BetaFromRequest, capm::CapmCalculator},
    },
    interface::{
        app_utils::{error, info},
        cache::{STOCK_LIST, STOCK_PAGE_DATA},
    },
};

struct StockSettings {
    window_days: u64,
    profit_type: ProfitRateCalculationType,
    market_index: MarketIndex,
}

static STOCK_SETTINGS: LazyLock<RwLock<StockSettings>> = LazyLock::new(|| {
    RwLock::new(StockSettings {
        window_days: 365,
        profit_type: ProfitRateCalculationType::Logarithm,
        market_index: MarketIndex::Csi300Index,
    })
});

#[tauri::command]
pub async fn set_stock_settings<R: Runtime>(
    app: AppHandle<R>,
    window_days: u64,
    profit_type: ProfitRateCalculationType,
    market_index: MarketIndex,
) -> Result<(), String> {
    {
        let mut guard = STOCK_SETTINGS.write().await;
        guard.window_days = window_days;
        guard.profit_type = profit_type;
        guard.market_index = market_index;
    }
    info!(app, "设置成功")?;
    Ok(())
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct StockPageData {
    name: Arc<str>,
    code: Arc<str>,
    annualized_profit_rate: f64,
    standard_deviation: f64,
    historic_stock_quote: Arc<[HistoricStockQuote]>,
    forward_adjusted_prices: Arc<[AssetPrice]>,
    beta: f64,
    capm: f64,
}

async fn get_stock_data(code: Arc<str>, name: Arc<str>) -> anyhow::Result<StockPageData> {
    let moved_code = code.clone();
    let adjusted_prices = tokio::spawn(async move {
        get_adjusted_share_price(moved_code, AdjustDirection::Forward).await
    });

    let moved_code = code.clone();
    let historic_quote =
        tokio::spawn(async move { HistoricStockQuote::from_api(moved_code, 240, 1023).await });

    let moved_code = code.clone();
    let (window_days, profit_type, market_index) = {
        let guard = STOCK_SETTINGS.read().await;
        (guard.window_days, guard.profit_type, guard.market_index)
    };

    let beta = tokio::spawn(async move {
        BetaFromRequest::new()
            .code(moved_code)
            .window_days(window_days)
            .profit_type(profit_type)
            .market(market_index)
            .calculate()
            .await
    });

    let risk_free_rate = tokio::spawn(async { get_risk_free_rate().await });

    let adjusted_share_price = adjusted_prices.await??;

    let share_prices = adjusted_share_price
        .iter()
        .map(|x| *x.price())
        .collect::<Vec<_>>();

    let annualized_profit_rate =
        annualized_return(&share_prices, profit_type).context("failed to calculate profit rate")?;
    let n = share_prices.len() as f64;
    let share_price_mean = share_prices.iter().sum::<f64>() / n;
    let standard_deviation = (share_prices
        .iter()
        .map(|x| (x - share_price_mean).powi(2))
        .sum::<f64>()
        / (n - 1.0))
        .sqrt();
    let beta = beta.await??;
    let rf = risk_free_rate.await??;
    let capm = CapmCalculator::new()
        .share_beta(beta)
        .code(code.as_ref())
        .market_index(market_index)
        .window_days(window_days)
        .risk_free_rate(rf)
        .calculate()
        .await?;

    let data = StockPageData {
        name,
        code,
        annualized_profit_rate,
        standard_deviation,
        historic_stock_quote: historic_quote.await??,
        forward_adjusted_prices: adjusted_share_price,
        beta,
        capm,
    };

    Ok(data)
}

#[tauri::command]
pub async fn stock_page_data<R: Runtime>(
    app: AppHandle<R>,
    code_or_name: Arc<str>,
) -> Result<Arc<StockPageData>, String> {
    info!(app, "正在获取数据 ...")?;
    let code_to_name = STOCK_LIST.code_to_name();
    let name_to_code = STOCK_LIST.name_to_code();
    if !code_to_name.contains_key(&code_or_name) && !name_to_code.contains_key(&code_or_name) {
        let err = format!("未知股票: {}", code_or_name);
        error!(app, &err)?;
        return Err(err);
    }

    let name: Arc<str>;
    let code: Arc<str>;

    if code_to_name.contains_key(&code_or_name) {
        code = code_or_name.clone();
        name = code_to_name
            .get(&code)
            .ok_or_else(|| "Invalid code".to_string())?
            .clone();
    } else {
        name = code_or_name.clone();
        code = name_to_code
            .get(&name)
            .ok_or_else(|| "Invalid name".to_string())?
            .clone();
    }

    if let Some(data) = STOCK_PAGE_DATA.get(&(name.clone(), code.clone())) {
        return Ok(data.clone());
    }

    let data = match get_stock_data(code, name).await {
        Ok(v) => v,
        Err(e) => {
            error!(app, e.to_string())?;
            return Err(e.to_string());
        }
    };

    info!(app, "数据加载完成！")?;
    Ok(Arc::new(data))
}

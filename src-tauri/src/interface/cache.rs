use std::sync::{Arc, LazyLock};

use dashmap::DashMap;
use derive_getters::Get<PERSON>;

use crate::interface::{stock_list::StockListData, stock_page::StockPageData};

#[derive(Debug, Getters)]
pub struct StockList {
    code_to_name: DashMap<Arc<str>, Arc<str>>,
    name_to_code: DashMap<Arc<str>, Arc<str>>,
}

impl StockList {
    pub fn new() -> Self {
        Self {
            code_to_name: DashMap::new(),
            name_to_code: DashMap::new(),
        }
    }

    pub fn update(&self, data: &Vec<StockListData>) {
        for stock in data {
            let name: Arc<str> = Arc::from(stock.name().as_str());
            let code: Arc<str> = Arc::from(stock.code().as_str());
            self.code_to_name.insert(code.clone(), name.clone());
            self.name_to_code.insert(name, code);
        }
    }
}

pub static STOCK_LIST: LazyLock<StockList> = LazyLock::new(StockList::new);

pub static STOCK_PAGE_DATA: LazyLock<DashMap<(Arc<str>, Arc<str>), Arc<StockPageData>>> =
    LazyLock::new(DashMap::new);

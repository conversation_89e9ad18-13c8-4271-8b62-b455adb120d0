use std::{io::Write, path::PathBuf, sync::LazyLock};

use chrono::{Local, NaiveDateTime};
use flume::{unbounded, Receiver, Sender};
use serde::Serialize;
use tauri::{Emitter, Runtime};

pub type AnyResult<T> = Result<T, String>;

static LOG_QUEUE: LazyLock<LogQueue> = LazyLock::new(|| LogQueue::new(LogLevel::Debug));

#[allow(dead_code)]
struct LogQueue {
    sender: Sender<LogRecord>,
    receiver: Receiver<LogRecord>,
    level: LogLevel,
}

impl LogQueue {
    fn new(level: LogLevel) -> Self {
        let (sender, receiver) = unbounded::<LogRecord>();
        Self {
            sender,
            receiver,
            level,
        }
    }

    fn get(&self) -> anyhow::Result<LogRecord> {
        let record = self.receiver.recv()?;
        Ok(record)
    }

    fn put(&self, record: LogRecord) -> anyhow::Result<()> {
        if self.level > record.level {
            return Ok(());
        }
        self.sender.send(record)?;
        Ok(())
    }
}

#[allow(dead_code)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize)]
pub enum LogLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

impl AsRef<str> for LogLevel {
    fn as_ref(&self) -> &str {
        match self {
            Self::Debug => "Debug",
            Self::Info => "Info",
            Self::Warning => "Warning",
            Self::Error => "Error",
            Self::Critical => "Critical",
        }
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct LogRecord {
    level: LogLevel,
    time: NaiveDateTime,
    message: String,
    file: String,
    line: u32,
}

impl LogRecord {
    pub fn format(&self) -> String {
        format!(
            "[{}][{}][{}:{}][{}]\n",
            self.time.format("%Y-%m-%d %H:%M:%S.%f").to_string(),
            self.level.as_ref(),
            self.file,
            self.line,
            self.message
        )
    }

    pub fn new(
        level: LogLevel,
        message: impl Into<String>,
        file: impl Into<String>,
        line: u32,
    ) -> Self {
        let now = Local::now().naive_local();
        Self {
            level,
            time: now,
            message: message.into(),
            file: file.into(),
            line,
        }
    }

    pub fn write(self) {
        if let Err(e) = LOG_QUEUE.put(self) {
            eprintln!("LogQueue Error: {e}");
        }
    }
}

pub fn bottom_text<R: Runtime>(app: &tauri::AppHandle<R>, record: LogRecord) -> AnyResult<()> {
    app.emit("bottom-text", &record)
        .map_err(|e| e.to_string())?;
    record.write();
    Ok(())
}

fn log_recorder(log_folder: PathBuf) -> anyhow::Result<()> {
    std::fs::create_dir_all(&log_folder)?;
    while let Ok(record) = LOG_QUEUE.get() {
        let now = Local::now().date_naive().format("%Y-%m-%d").to_string();
        let filepath = &log_folder.join(format!("{}.log", now));
        let mut file = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(&filepath)?;
        file.write(record.format().as_bytes())?;
    }

    Ok(())
}

pub fn init_log_recorder(log_folder: impl Into<PathBuf>) -> anyhow::Result<()> {
    let log_folder = log_folder.into();
    std::thread::spawn(move || {
        if let Err(e) = log_recorder(log_folder) {
            eprintln!("Log recorder stopped: {}", e);
        }
    });
    Ok(())
}

#[allow(unused)]
macro_rules! log {
    ($message: expr, $level: expr) => {{
        crate::interface::app_utils::LogRecord::new($level, $message, file!(), line!()).write();
    }};
    ($app: expr, $message: expr, $level: expr) => {{
        let record =
            crate::interface::app_utils::LogRecord::new($level, $message, file!(), line!());
        crate::interface::app_utils::bottom_text(&$app, record)
    }};
}

#[allow(unused)]
macro_rules! debug {
    ($message: expr) => {{
        log!($message, crate::interface::app_utils::LogLevel::Debug);
    }};
    ($app: expr, $message: expr) => {
        log!($app, $message, crate::interface::app_utils::LogLevel::Debug)
    };
}

#[allow(unused)]
macro_rules! info {
    ($message: expr) => {{
        log!($message, crate::interface::app_utils::LogLevel::Info);
    }};
    ($app: expr, $message: expr) => {
        crate::interface::app_utils::log!(
            $app,
            $message,
            crate::interface::app_utils::LogLevel::Info
        )
    };
}

#[allow(unused)]
macro_rules! warning {
    ($message: expr) => {{
        log!($message, crate::interface::app_utils::LogLevel::Warning);
    }};
    ($app: expr, $message: expr) => {
        crate::interface::app_utils::log!(
            $app,
            $message,
            crate::interface::app_utils::LogLevel::Warning
        )
    };
}

#[allow(unused)]
macro_rules! error {
    ($message: expr) => {{
        crate::interface::app_utils::log!($message, LogLevel::Error);
    }};
    ($app: expr, $message: expr) => {
        crate::interface::app_utils::log!(
            $app,
            $message,
            crate::interface::app_utils::LogLevel::Error
        )
    };
}

#[allow(unused)]
macro_rules! critical {
    ($message: expr) => {{
        crate::interface::app_utils::log!(
            $message,
            crate::interface::app_utils::LogLevel::Critical
        );
    }};
    ($app: expr, $message: expr) => {
        crate::interface::app_utils::log!(
            $app,
            $message,
            crate::interface::app_utils::LogLevel::Critical
        )
    };
}

#[allow(unused)]
pub(crate) use {critical, debug, error, info, log, warning};

#[test]
fn test_level() {
    assert!(LogLevel::Info > LogLevel::Debug);
    assert!(LogLevel::Warning > LogLevel::Info);
    assert!(LogLevel::Error > LogLevel::Warning);
    assert!(LogLevel::Critical > LogLevel::Error);
}

#[test]
fn test_file_macro() {
    println!("{}", file!());
}

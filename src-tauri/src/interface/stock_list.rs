use derive_getters::Getters;
use reqwest::ClientBuilder;
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};
use tauri::{Manager, Runtime};

use crate::interface::{app_utils::info, cache::STOCK_LIST};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Getters)]
pub struct StockListData {
    code: String,
    name: String,
    #[serde(rename = "exchangeCode")]
    exchange_code: String,
    #[serde(rename = "countryCode")]
    country_code: String,
    #[serde(rename = "type")]
    asset_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct StockListResponse {
    msg: String,
    code: u16,
    data: Vec<StockListData>,
}

impl StockListResponse {
    fn take_data(self) -> Vec<StockListData> {
        self.data
    }
}

async fn get_stock_list() -> anyhow::Result<Vec<StockListData>> {
    let url = "https://miana.com.cn/api/stock/v1/stockList?token=38a55ce566135ea39874ddbfba94b225&countryCode=CHN";
    let client = ClientBuilder::new().no_proxy().build()?;
    let response = client.get(url).send().await?.text().await?;
    let response = serde_json::from_str::<StockListResponse>(&response)?;
    let data = response.take_data();
    STOCK_LIST.update(&data);
    Ok(data)
}

fn get_stock_cache_path<R: Runtime>(
    app: &tauri::AppHandle<R>,
) -> Result<std::path::PathBuf, String> {
    let dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    std::fs::create_dir_all(&dir).map_err(|e| e.to_string())?;
    Ok(dir.join("stock_list.json"))
}

#[tauri::command]
pub async fn stock_list<R: Runtime>(
    app: tauri::AppHandle<R>,
) -> Result<Vec<StockListData>, String> {
    info!(app, "正在读取股票列表...")?;
    let stock = get_stock_cache_path(&app)?;
    if !stock.exists() {
        let data = get_stock_list().await.map_err(|e| e.to_string())?;
        let json = serde_json::to_string_pretty(&data).map_err(|e| e.to_string())?;
        tokio::fs::write(&stock, json)
            .await
            .map_err(|e| e.to_string())?;
        return Ok(data);
    }

    let modified = stock
        .metadata()
        .and_then(|x| x.modified())
        .map_err(|e| e.to_string())?
        .duration_since(UNIX_EPOCH)
        .map(|x| x.as_secs())
        .map_err(|e| e.to_string())?;

    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map(|x| x.as_secs())
        .map_err(|e| e.to_string())?;

    let data = if now - modified > 3600 * 24 {
        let data = get_stock_list().await.map_err(|e| e.to_string())?;
        let json = serde_json::to_string_pretty(&data).map_err(|e| e.to_string())?;
        tokio::fs::write(&stock, json)
            .await
            .map_err(|e| e.to_string())?;
        data
    } else {
        let json = tokio::fs::read_to_string(&stock)
            .await
            .map_err(|e| e.to_string())?;
        let data = serde_json::from_str::<Vec<StockListData>>(&json).map_err(|e| e.to_string())?;
        STOCK_LIST.update(&data);
        data
    };
    info!(app, "股票列表读取成功")?;
    Ok(data)
}

#[tokio::test]
async fn test_api() {
    std::env::set_var("RUST_BACKTRACE", "1");
    let response = get_stock_list().await.unwrap();
    println!("{:#?}", response);
}

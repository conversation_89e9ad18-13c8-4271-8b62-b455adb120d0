use anyhow::bail;

pub fn calculate_correlation_coefficient(
    arr1: &[f64],
    arr2: &[f64],
    std1: f64,
    std2: f64,
) -> anyhow::Result<f64> {
    let cov = covariance(arr1, arr2)?;
    let denom = std1 * std2;
    if denom == 0.0 {
        bail!("zero variance in one of the arrays");
    }
    Ok(cov / denom)
}

pub fn covariance(arr1: &[f64], arr2: &[f64]) -> anyhow::Result<f64> {
    let n = arr1.len();
    if n != arr2.len() {
        bail!("the length of arr1 is not equal to the length of arr2");
    }

    if n < 2 {
        bail!("at least two data points are required");
    }

    let n = n as f64;
    let arr1_mean = arr1.iter().sum::<f64>() / n;
    let arr2_mean = arr2.iter().sum::<f64>() / n;

    Ok(arr1
        .iter()
        .zip(arr2.iter())
        .map(|(x1, x2)| (x1 - arr1_mean) * (x2 - arr2_mean))
        .sum::<f64>()
        / (n - 1.0))
}

use tauri::Manager;

pub mod data_source;
pub mod efficient_frontier;
pub mod indicators;
pub mod interface;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .setup(|app| {
            let app_handle = app.handle().app_handle();
            let folder = app_handle.path().app_data_dir()?.join("log");
            interface::app_utils::init_log_recorder(folder)?;
            app.webview_windows().iter().for_each(|w| {
                w.1.maximize().unwrap();
            });
            Ok(())
        })
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            interface::stock_list::stock_list,
            interface::stock_page::set_stock_settings,
            interface::stock_page::stock_page_data,
            interface::portfolio::optimize_min_variance,
            interface::portfolio::optimize_min_variance_given_return,
            interface::portfolio::optimize_max_return_given_variance,
            interface::portfolio::optimize_max_sharp_ratio,
            interface::portfolio::optimize_risk_parity,
            interface::portfolio::draw_efficient_frontier
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tokio::test]
async fn test_api() {
    let url = "http://hq.sinajs.cn/list=sz002351,sh600519";
    let response = reqwest::Client::new()
        .get(url)
        .header("Referer", "https://finance.sina.com.cn/")
        .header("host", "hq.sinajs.cn")
        .send()
        .await
        .unwrap();
    println!("{}", response.text().await.unwrap());
}

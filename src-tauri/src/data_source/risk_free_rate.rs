use anyhow::{anyhow, bail, Context};
use chrono::{Local, NaiveDate};
use futures::stream::{FuturesUnordered, StreamExt};
use reqwest::Client;
use std::sync::OnceLock;
use std::{sync::Arc, time::UNIX_EPOCH};

static RISK_FREE_RATE: OnceLock<f64> = OnceLock::new();

async fn get_risk_free_bond_info() -> anyhow::Result<serde_json::Value> {
    let form_data = [
        ("pageNo", "1"),
        ("pageSize", "15"),
        ("bondName", ""),
        ("bondCode", ""),
        ("issueEnty", ""),
        ("bondType", "100001"),
        ("bondSpclPrjctVrty", ""),
        ("couponType", "0"),
        ("issueYear", ""),
        ("entyDefinedCode", ""),
        ("rtngShrt", ""),
    ];
    let client = Arc::new(Client::new());
    let response = client
        .post("https://www.chinamoney.com.cn/ags/ms/cm-u-bond-md/BondMarketInfoList2")
        .form(&form_data)
        .send()
        .await?
        .json::<serde_json::Value>()
        .await?;

    let data = response
        .pointer("/data/resultList")
        .and_then(|x| x.as_array())
        .and_then(|x| {
            x.into_iter()
                .map(|x| {
                    x.as_object()
                        .and_then(|m| m.get("bondDefinedCode"))
                        .and_then(|v| v.as_str())
                })
                .collect::<Option<Vec<_>>>()
        })
        .context("failed to parse data")?;

    let forms = data
        .into_iter()
        .map(|x| Arc::new([("bondDefinedCode", x)]))
        .collect::<Vec<_>>();
    let url: &str = "https://www.chinamoney.com.cn/ags/ms/cm-u-bond-md/BondDetailInfo";

    let mut handles = FuturesUnordered::new();

    for form in forms {
        let c = client.clone();
        handles.push(async move {
            let response = c
                .post(url)
                .form(&form)
                .send()
                .await?
                .json::<serde_json::Value>()
                .await?;

            Ok::<serde_json::Value, anyhow::Error>(response)
        });
    }

    while let Some(res) = handles.next().await {
        let res = res?;
        let period = match res
            .pointer("/data/bondBaseInfo/bondPeriod")
            .and_then(|x| x.as_str())
        {
            Some(v) => v,
            None => continue,
        };

        if period == "1年" {
            return Ok(res);
        }
    }

    bail!("failed to get risk free rate")
}

fn yield_to_maturity(
    coupon_rate: f64,
    par_value: f64,
    market_price: f64,
    maturity: NaiveDate,
) -> f64 {
    let now = Local::now().naive_local().date();
    let days = (maturity - now).num_days();
    ((par_value * (1.0 + coupon_rate)) / market_price).powf(365.0 / (days as f64)) - 1.0
}

pub async fn get_risk_free_rate() -> anyhow::Result<f64> {
    if let Some(&rf) = RISK_FREE_RATE.get() {
        return Ok(rf);
    }
    let info = get_risk_free_bond_info().await?;
    let bond_code = info
        .pointer("/data/bondBaseInfo/bondDefinedCode")
        .and_then(|x| x.as_str())
        .context("failed to get bond code")?;

    let timestamp = std::time::SystemTime::now();
    let time_ms = timestamp.duration_since(UNIX_EPOCH)?.as_millis();
    let url = format!(
        "https://www.chinamoney.com.cn/ags/ms/cm-u-md-bond/CbtPri?t={}",
        time_ms
    );

    let response = Client::new()
        .post(url)
        .form(&[("bondDefinedCode", bond_code)])
        .send()
        .await?
        .json::<serde_json::Value>()
        .await?;
    let par_value = info
        .pointer("/data/bondBaseInfo/parValue")
        .and_then(|x| x.as_str())
        .and_then(|x| x.parse::<f64>().ok());

    let market_price = if let Some(v) = response
        .pointer("/records/0/dmiLatestRateLabel")
        .and_then(|x| x.as_str())
        .and_then(|x| x.parse::<f64>().ok())
    {
        v
    } else {
        par_value.context("failed to get bond market price")?
    };

    let data = info
        .pointer("/data/bondBaseInfo")
        .context("failed to get bond data")?;

    let maturity_date = data
        .pointer("/mrtyDate")
        .and_then(|x| x.as_str())
        .and_then(|x| NaiveDate::parse_from_str(x, "%Y-%m-%d").ok())
        .context("failed to get maturity date")?;

    let coupon_rate = data
        .pointer("/parCouponRate")
        .and_then(|x| x.as_str())
        .and_then(|x| x.parse::<f64>().ok().map(|v| v / 100.0))
        .context("failed to parse coupon rate")?;

    let par_value = data
        .pointer("/parValue")
        .and_then(|x| x.as_str())
        .and_then(|x| x.parse::<f64>().ok())
        .context("failed to parse par value")?;

    let rf = yield_to_maturity(coupon_rate, par_value, market_price, maturity_date);
    RISK_FREE_RATE.set(rf).map_err(|e| anyhow!(e))?;
    Ok(rf)
}

#[test]
fn timestamp() {
    let timestamp = std::time::SystemTime::now();
    let time_sec = timestamp.duration_since(UNIX_EPOCH).unwrap().as_millis();
    println!("{:?}", time_sec);
}

#[tokio::test]
async fn test_bond() {
    std::env::set_var("RUST_BACKTRACE", "1");
    let rf = get_risk_free_rate().await.unwrap();
    println!("{}", rf);
}

use std::sync::{Arc, LazyLock};

use anyhow::Context;
use chrono::NaiveDate;
use dashmap::DashMap;
use derive_getters::Getters;
use reqwest::Client;
use serde::{Deserialize, Serialize};

use crate::data_source::code_to_symbol::code_to_symbol;

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Hash, Eq)]
pub enum AdjustDirection {
    Forward,
    Backword,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Get<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AssetPrice {
    date: NaiveDate,
    price: f64,
}

static ADJUSTED_SHARE_PRICE_CAHCE: LazyLock<DashMap<(String, AdjustDirection), Arc<[AssetPrice]>>> =
    LazyLock::new(DashMap::new);

pub async fn get_adjusted_share_price(
    code: impl AsRef<str>,
    adjust_direction: AdjustDirection,
) -> anyhow::Result<Arc<[AssetPrice]>> {
    if let Some(v) = ADJUSTED_SHARE_PRICE_CAHCE.get(&(code.as_ref().to_string(), adjust_direction))
    {
        return Ok(v.clone());
    }
    let direction = match adjust_direction {
        AdjustDirection::Backword => "houfuquan",
        AdjustDirection::Forward => "qianfuquan",
    };
    let symbol = code_to_symbol(code.as_ref())
        .with_context(|| format!("failed to find symbol for code {}", code.as_ref()))?;

    let url = format!(
        "http://finance.sina.com.cn/realstock/company/{}/{}.js",
        symbol, direction
    );

    let response = Client::new().get(url).send().await?.text().await?;

    let data = json5::from_str::<serde_json::Value>(&response)?;
    let data = data
        .pointer("/0/data")
        .and_then(|x| x.as_object())
        .context("failed to parse data")?;

    let mut result: Vec<AssetPrice> = Vec::with_capacity(data.len());

    for (key, value) in data.into_iter() {
        let date = NaiveDate::parse_from_str(key, "_%Y_%m_%d")?;
        let price = value
            .as_str()
            .and_then(|x| x.parse::<f64>().ok())
            .context("failed to parse share price")?;
        result.push(AssetPrice { date, price });
    }
    let result: Arc<[AssetPrice]> = Arc::from(result);

    ADJUSTED_SHARE_PRICE_CAHCE.insert(
        (code.as_ref().to_string(), adjust_direction),
        result.clone(),
    );

    Ok(result.clone())
}

#[tokio::test]
async fn test_adjusted_share_price() {
    let res = get_adjusted_share_price("sz002351", AdjustDirection::Forward)
        .await
        .unwrap();

    println!("{:#?}", res);
}

use std::sync::{Arc, LazyLock};

use super::code_to_symbol::code_to_symbol;
use anyhow::Context;
use chrono::NaiveDate;
use dashmap::DashMap;
use derive_getters::Getters;
use reqwest::Client;
use serde::de::{self, Deserializer};
use serde::{Deserialize, Serialize};

#[derive(Debu<PERSON>, <PERSON>lone, Deserialize, Get<PERSON>)]
pub struct StockQuote {
    stock_name: String,
    today_open_price: f64,
    yesterday_close_price: f64,
    current_price: f64,
    today_high_price: f64,
    today_low_price: f64,
    bid_price: f64,
    ask_price: f64,
    volume_traded: u32,
    turnover: f64,
    buy_1_volume: u32,
    buy_1_price: f64,
    buy_2_volume: u32,
    buy_2_price: f64,
    buy_3_volume: u32,
    buy_3_price: f64,
    buy_4_volume: u32,
    buy_4_price: f64,
    buy_5_volume: u32,
    buy_5_price: f64,
    sell_1_volume: u32,
    sell_1_price: f64,
    sell_2_volume: u32,
    sell_2_price: f64,
    sell_3_volume: u32,
    sell_3_price: f64,
    sell_4_volume: u32,
    sell_4_price: f64,
    sell_5_volume: u32,
    sell_5_price: f64,
    date: String,
    time: String,
}

impl StockQuote {
    fn from_api_str(s: &str) -> Option<Self> {
        // 去掉 var 前缀
        let eq_pos = s.find('=')?;
        let mut content = &s[eq_pos + 1..];

        // 去掉开头和结尾的引号以及分号
        content = content
            .trim()
            .trim_start_matches('"')
            .trim_end_matches('"')
            .trim_end_matches(';');

        let parts: Vec<&str> = content.split(',').collect();
        if parts.len() < 32 {
            // 注意字段数根据实际 API 字段调整
            return None;
        }

        Some(Self {
            stock_name: parts[0].to_string(),
            today_open_price: parts[1].parse().ok()?,
            yesterday_close_price: parts[2].parse().ok()?,
            current_price: parts[3].parse().ok()?,
            today_high_price: parts[4].parse().ok()?,
            today_low_price: parts[5].parse().ok()?,
            bid_price: parts[6].parse().ok()?,
            ask_price: parts[7].parse().ok()?,
            volume_traded: parts[8].parse().ok()?,
            turnover: parts[9].parse().ok()?,
            buy_1_volume: parts[10].parse().ok()?,
            buy_1_price: parts[11].parse().ok()?,
            buy_2_volume: parts[12].parse().ok()?,
            buy_2_price: parts[13].parse().ok()?,
            buy_3_volume: parts[14].parse().ok()?,
            buy_3_price: parts[15].parse().ok()?,
            buy_4_volume: parts[16].parse().ok()?,
            buy_4_price: parts[17].parse().ok()?,
            buy_5_volume: parts[18].parse().ok()?,
            buy_5_price: parts[19].parse().ok()?,
            sell_1_volume: parts[20].parse().ok()?,
            sell_1_price: parts[21].parse().ok()?,
            sell_2_volume: parts[22].parse().ok()?,
            sell_2_price: parts[23].parse().ok()?,
            sell_3_volume: parts[24].parse().ok()?,
            sell_3_price: parts[25].parse().ok()?,
            sell_4_volume: parts[26].parse().ok()?,
            sell_4_price: parts[27].parse().ok()?,
            sell_5_volume: parts[28].parse().ok()?,
            sell_5_price: parts[29].parse().ok()?,
            date: parts[30].to_string(),
            time: parts[31].to_string(),
        })
    }

    pub async fn from_api(code: impl AsRef<[&str]>) -> anyhow::Result<Vec<Self>> {
        let code = code
            .as_ref()
            .iter()
            .map(|x| code_to_symbol(*x))
            .collect::<Option<Vec<_>>>()
            .context("invalid code")?;
        let url = format!("http://hq.sinajs.cn/list={}", code.join(","));

        let response = reqwest::Client::new()
            .post(url)
            .header("Referer", "https://finance.sina.com.cn/")
            .send()
            .await?
            .text()
            .await?;

        let lines = response.trim().split('\n').collect::<Vec<_>>();
        let lines = lines
            .into_iter()
            .map(|x| Self::from_api_str(x))
            .collect::<Option<Vec<_>>>()
            .context("failed to parse api data")?;

        Ok(lines)
    }
}

fn de_from_str<'de, D, T>(deserializer: D) -> Result<T, D::Error>
where
    D: Deserializer<'de>,
    T: std::str::FromStr,
    <T as std::str::FromStr>::Err: std::fmt::Display,
{
    let s = String::deserialize(deserializer)?;
    s.parse::<T>().map_err(de::Error::custom)
}

fn de_from_date<'de, D>(deserializer: D) -> Result<NaiveDate, D::Error>
where
    D: Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    NaiveDate::parse_from_str(&s, "%Y-%m-%d").map_err(de::Error::custom)
}

static HISTORIC_STOCK_QUOTE_CACHE: LazyLock<
    DashMap<(String, u64, u64), Arc<[HistoricStockQuote]>>,
> = LazyLock::new(DashMap::new);

#[derive(Debug, Clone, Deserialize, Getters, Serialize)]
pub struct HistoricStockQuote {
    #[serde(deserialize_with = "de_from_date")]
    day: NaiveDate,
    #[serde(deserialize_with = "de_from_str")]
    open: f64,
    #[serde(deserialize_with = "de_from_str")]
    high: f64,
    #[serde(deserialize_with = "de_from_str")]
    low: f64,
    #[serde(deserialize_with = "de_from_str")]
    close: f64,
    #[serde(deserialize_with = "de_from_str")]
    volume: u64,
}

impl HistoricStockQuote {
    pub async fn from_api(
        code: impl AsRef<str>,
        scale: u64,
        datalen: u64,
    ) -> anyhow::Result<Arc<[Self]>> {
        if let Some(v) =
            HISTORIC_STOCK_QUOTE_CACHE.get(&(code.as_ref().to_string(), scale, datalen))
        {
            return Ok(v.clone());
        }

        let url = format!(
            "http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData?symbol={}&scale={}&ma=no&datalen={}",
            code_to_symbol(code.as_ref()).context("failed to process stock code")?,
            scale, datalen
        );

        let response = Client::new()
            .get(url)
            .header("Referer", "https://finance.sina.com.cn/")
            .send()
            .await?
            .json::<Vec<Self>>()
            .await?;
        let response: Arc<[HistoricStockQuote]> = Arc::from(response);
        HISTORIC_STOCK_QUOTE_CACHE.insert(
            (code.as_ref().to_string(), scale, datalen),
            response.clone(),
        );
        Ok(response)
    }
}

#[tokio::test]
async fn test_lines() {
    let code = [
        "sh000001", "002351", "002670", "002104", "002017", "601138", "002456", "002436", "002049",
    ];
    let response = StockQuote::from_api(code).await;
    println!("{:#?}", response);
}

#[tokio::test]
async fn test_klen() {
    let res = HistoricStockQuote::from_api("sh000001", 240, 365)
        .await
        .unwrap();

    println!("{:#?}", res);
}

#[tokio::test]
async fn test_index() {
    let stock = StockQuote::from_api(["sh000300"]).await.unwrap();
    println!("{:#?}", stock)
}

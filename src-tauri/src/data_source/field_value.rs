pub enum FieldValue<'a> {
    Str(&'a str),
    Float(f64),
    None,
}

impl<'a> FieldValue<'a> {
    pub fn to_owned(self) -> FieldValueOwned {
        match self {
            Self::Float(x) => FieldValueOwned::Float(x),
            Self::Str(x) => FieldValueOwned::Str(x.to_owned()),
            Self::None => FieldValueOwned::None,
        }
    }
}

pub enum FieldValueOwned {
    Str(String),
    Float(f64),
    None,
}

impl FieldValueOwned {
    pub fn as_ref(&self) -> FieldValue<'_> {
        match self {
            Self::Float(x) => FieldValue::Float(*x),
            Self::Str(x) => FieldValue::Str(x),
            Self::None => FieldValue::None,
        }
    }
}

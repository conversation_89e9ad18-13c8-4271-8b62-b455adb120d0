use std::sync::Arc;

use anyhow::{bail, Context};
use chrono::{Days, Local};

use crate::{
    data_source::{risk_free_rate::get_risk_free_rate, stock_quote::HistoricStockQuote},
    indicators::{
        calculations::{annualized_return, MarketIndex, ProfitRateCalculationType},
        capm::beta::BetaFromRequest,
    },
};

fn calculate_capm(
    market_prices: &[f64],
    risk_free_rate: f64,
    share_beta: f64,
    profit_type: ProfitRateCalculationType,
) -> Option<f64> {
    let market_profit_rate = annualized_return(market_prices, profit_type)?;
    Some(risk_free_rate + share_beta * (market_profit_rate - risk_free_rate))
}

async fn get_market_prices(
    market_prices: Option<Arc<[HistoricStockQuote]>>,
    market_index: Option<MarketIndex>,
    datalen: u64,
) -> anyhow::Result<Arc<[HistoricStockQuote]>> {
    match &market_prices {
        Some(v) => Ok(v.clone()),
        None => {
            let market_code = market_index.context("lack of market index")?.get_code();
            let market_prices = HistoricStockQuote::from_api(market_code, 240, datalen).await?;

            Ok(market_prices)
        }
    }
}

async fn capm_risk_free_rate(rf: Option<f64>) -> anyhow::Result<f64> {
    match rf {
        Some(v) => Ok(v),
        None => get_risk_free_rate().await,
    }
}

pub struct CapmCalculator<T: AsRef<str>> {
    market_prices: Option<Arc<[HistoricStockQuote]>>,
    market_index: Option<MarketIndex>,
    risk_free_rate: Option<f64>,
    share_beta: Option<f64>,
    code: Option<T>,
    profit_type: ProfitRateCalculationType,
    window_days: u64,
}

impl<T: AsRef<str>> CapmCalculator<T> {
    pub fn new() -> Self {
        Self {
            market_prices: None,
            risk_free_rate: None,
            share_beta: None,
            code: None,
            market_index: None,
            profit_type: ProfitRateCalculationType::Logarithm,
            window_days: 365,
        }
    }

    pub fn window_days(mut self, window_days: u64) -> Self {
        self.window_days = window_days;
        self
    }

    pub fn market_prices(mut self, market_prices: Arc<[HistoricStockQuote]>) -> Self {
        self.market_prices = Some(market_prices);
        self
    }

    pub fn risk_free_rate(mut self, rf: f64) -> Self {
        self.risk_free_rate = Some(rf);
        self
    }

    pub fn share_beta(mut self, beta: f64) -> Self {
        self.share_beta = Some(beta);
        self
    }

    pub fn profit_type(mut self, profit_type: ProfitRateCalculationType) -> Self {
        self.profit_type = profit_type;
        self
    }

    pub fn code(mut self, code: T) -> Self {
        self.code = Some(code);
        self
    }

    pub fn market_index(mut self, market_index: MarketIndex) -> Self {
        self.market_index = Some(market_index);
        self
    }

    pub async fn calculate(self) -> anyhow::Result<f64> {
        if self.code.is_none() && self.share_beta.is_none() {
            bail!("lack of share code or share beta!")
        }

        if self.market_prices.is_none() && self.market_index.is_none() {
            bail!("lack of market prices or market index")
        }

        let market_prices = self.market_prices.clone();
        let code: Arc<str> = Arc::from(self.code.context("failed to get code")?.as_ref());
        let market_index = self.market_index;
        let market_prices = tokio::spawn(async move {
            get_market_prices(market_prices, market_index, self.window_days).await
        });
        let rf = self.risk_free_rate;
        let rf = tokio::spawn(async move { capm_risk_free_rate(rf).await });

        let now = Local::now().naive_local().date();
        let start_date = now - Days::new(self.window_days);
        let market_prices: Arc<[HistoricStockQuote]> = Arc::from(
            market_prices
                .await??
                .into_iter()
                .filter(|x| x.day() >= &start_date)
                .cloned()
                .collect::<Vec<_>>(),
        );

        let beta = match self.share_beta {
            Some(v) => v,
            None => {
                BetaFromRequest::new()
                    .code(code)
                    .market_prices(market_prices.clone())
                    .calculate()
                    .await?
            }
        };

        let market_prices = market_prices
            .iter()
            .map(|x| *x.close())
            .collect::<Vec<f64>>();
        let rf = rf.await??;

        let capm = calculate_capm(&market_prices, rf, beta, self.profit_type)
            .context("failed to get capm")?;
        Ok(capm)
    }
}

#[tokio::test]
async fn test_capm() {
    let capm = CapmCalculator::new()
        .code("002351")
        .market_index(MarketIndex::Csi300Index)
        .calculate()
        .await
        .unwrap();

    println!("{}", capm);
}

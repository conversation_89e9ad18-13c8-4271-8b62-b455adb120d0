use std::sync::Arc;

use anyhow::Context;
use chrono::{Days, Local};

use crate::data_source::{
    adjusted_share_price::{get_adjusted_share_price, AdjustDirection, AssetPrice},
    stock_quote::HistoricStockQuote,
};
use crate::indicators::calculations::{
    rolling_profit_rate, MarketIndex, ProfitRateCalculationType,
};

pub fn calculate_beta(
    share_price: &[f64],
    market_price: &[f64],
    profit_type: ProfitRateCalculationType,
) -> Option<f64> {
    if share_price.len() != market_price.len() {
        return None;
    }

    let share_profit_rate = rolling_profit_rate(share_price, profit_type);
    let market_profit_rate = rolling_profit_rate(market_price, profit_type);
    let n = share_profit_rate.len();

    let avg_share_profit_rate = share_profit_rate.iter().sum::<f64>() / (n as f64);
    let avg_market_profit_rate = market_profit_rate.iter().sum::<f64>() / (n as f64);

    let cov = share_profit_rate
        .iter()
        .zip(market_profit_rate.iter())
        .map(|(rs, rm)| (rs - avg_share_profit_rate) * (rm - avg_market_profit_rate))
        .sum::<f64>()
        / (n as f64 - 1.0);

    let var_market = market_profit_rate
        .iter()
        .map(|x| (x - avg_market_profit_rate).powi(2))
        .sum::<f64>()
        / (n as f64 - 1.0);

    Some(cov / var_market)
}

struct AlignedPrices {
    share_price: Vec<f64>,
    market_price: Vec<f64>,
}

fn align_market_price_share_price(
    share_price: &[&AssetPrice],
    market_prices: &[&HistoricStockQuote],
) -> AlignedPrices {
    let mut ptr_m: usize = 0;
    let mut ptr_f: usize = 0;

    let mut market_prices_f64: Vec<f64> = Vec::with_capacity(market_prices.len());
    let mut forward_adj_share_prices_f64: Vec<f64> = Vec::with_capacity(share_price.len());

    while ptr_m < market_prices.len() && ptr_f < share_price.len() {
        let m = &market_prices[ptr_m];
        let f = &share_price[ptr_f];
        if m.day() == f.date() {
            market_prices_f64.push(*m.close());
            forward_adj_share_prices_f64.push(*f.price());
            ptr_f += 1;
            ptr_m += 1
        } else if m.day() > f.date() {
            ptr_f += 1;
        } else {
            ptr_m += 1;
        }
    }

    AlignedPrices {
        share_price: forward_adj_share_prices_f64,
        market_price: market_prices_f64,
    }
}

async fn beta_from_request(
    code: impl AsRef<str>,
    profit_type: ProfitRateCalculationType,
    market: MarketIndex,
    window_days: u64,
) -> anyhow::Result<f64> {
    let market_code = market.get_code();
    let now = Local::now().naive_local().date();
    let start_date = now - Days::new(window_days);

    let code = Arc::new(code.as_ref().to_owned());
    let market_code = Arc::new(market_code.to_owned());
    let forward_adj_share_prices = tokio::spawn(async move {
        get_adjusted_share_price(code.as_ref(), AdjustDirection::Forward).await
    });
    let market_prices = tokio::spawn(async move {
        HistoricStockQuote::from_api(market_code.as_ref(), 240, window_days).await
    });

    let forward_adj_share_prices = forward_adj_share_prices.await??;
    let forward_adj_share_prices = forward_adj_share_prices
        .iter()
        .filter(|x| x.date() >= &start_date)
        .collect::<Vec<_>>();

    let market_prices = market_prices.await??;
    let market_prices = market_prices
        .iter()
        .filter(|x| x.day() >= &start_date)
        .collect::<Vec<_>>();

    let aligned = align_market_price_share_price(&forward_adj_share_prices, &market_prices);

    let beta = calculate_beta(&aligned.share_price, &aligned.market_price, profit_type)
        .context("failed to get beta result")?;
    Ok(beta)
}

pub struct BetaFromRequest<T: AsRef<str>> {
    code: Option<T>,
    profit_type: ProfitRateCalculationType,
    market: MarketIndex,
    window_days: u64,
    share_prices: Option<Arc<[AssetPrice]>>,
    market_prices: Option<Arc<[HistoricStockQuote]>>,
}

impl<T: AsRef<str>> BetaFromRequest<T> {
    pub fn new() -> Self {
        Self {
            code: None,
            profit_type: ProfitRateCalculationType::Logarithm,
            market: MarketIndex::Csi300Index,
            window_days: 365,
            share_prices: None,
            market_prices: None,
        }
    }
    pub fn code(mut self, code: T) -> Self {
        self.code = Some(code);
        self
    }

    pub fn profit_type(mut self, profit_type: ProfitRateCalculationType) -> Self {
        self.profit_type = profit_type;
        self
    }

    pub fn market(mut self, market: MarketIndex) -> Self {
        self.market = market;
        self
    }

    pub fn window_days(mut self, window_days: u64) -> Self {
        self.window_days = window_days;
        self
    }

    pub fn market_prices(mut self, market_prices: Arc<[HistoricStockQuote]>) -> Self {
        self.market_prices = Some(market_prices);
        self
    }

    pub async fn calculate(self) -> anyhow::Result<f64> {
        let code = self.code.context("code not set")?;
        if self.share_prices.is_none() && self.market_prices.is_none() {
            beta_from_request(code, self.profit_type, self.market, self.window_days).await
        } else {
            let share_price = match self.share_prices {
                Some(v) => v,
                None => {
                    let share_prices_owned =
                        get_adjusted_share_price(code, AdjustDirection::Forward).await?;

                    share_prices_owned
                }
            };

            let market_prices = match self.market_prices {
                Some(v) => v,
                None => {
                    let market_code = self.market.get_code();
                    HistoricStockQuote::from_api(market_code, 240, self.window_days).await?
                }
            };

            let share_price_ref: Vec<&AssetPrice> = share_price.iter().collect();
            let mareket_price_ref: Vec<&HistoricStockQuote> = market_prices.iter().collect();
            let aligned = align_market_price_share_price(&share_price_ref, &mareket_price_ref);

            let beta = calculate_beta(
                &aligned.share_price,
                &aligned.market_price,
                self.profit_type,
            )
            .context("failed to calculate beta")?;

            Ok(beta)
        }
    }
}

#[tokio::test]
async fn test_beta_builder() {
    let beta = BetaFromRequest::new()
        .code("002436")
        .calculate()
        .await
        .unwrap();
    println!("{}", beta);
}

#[tokio::test]
async fn test_beta() {
    let beta = beta_from_request(
        "002436",
        ProfitRateCalculationType::Logarithm,
        MarketIndex::Csi300Index,
        365,
    )
    .await
    .unwrap();
    println!("{}", beta);
}

#[test]
fn t() {
    let now = Local::now().naive_local().date();
    println!("{}", now - Days::new(365));
}

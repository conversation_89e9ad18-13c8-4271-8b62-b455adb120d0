use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub enum ProfitRateCalculationType {
    Logarithm,
    Arithmetic,
}

#[derive(Debug, <PERSON>lone, Copy, Deserialize, Serialize)]
pub enum MarketIndex {
    SseCompositeIndex, // 上证
    Csi300Index,       // 沪深300
}

impl MarketIndex {
    pub fn get_code(&self) -> &'static str {
        match self {
            Self::SseCompositeIndex => "sh000001",
            Self::Csi300Index => "sh000300",
        }
    }
}

pub fn rolling_profit_rate(price: &[f64], profit_type: ProfitRateCalculationType) -> Vec<f64> {
    if price.is_empty() {
        return vec![];
    }
    match profit_type {
        ProfitRateCalculationType::Arithmetic => {
            price.windows(2).map(|x| (x[1] - x[0]) / x[0]).collect()
        }
        ProfitRateCalculationType::Logarithm => price
            .windows(2)
            .map(|x| f64::ln(x[1] / x[0]))
            .collect::<Vec<_>>(),
    }
}

pub fn annualized_return(price: &[f64], profit_type: ProfitRateCalculationType) -> Option<f64> {
    let daily_returns = rolling_profit_rate(price, profit_type);
    let n = daily_returns.len() as f64;
    if n == 0.0 {
        return None;
    }
    let trading_days = 252.0; // 一年交易日

    match profit_type {
        ProfitRateCalculationType::Arithmetic => {
            let avg_daily = daily_returns.iter().sum::<f64>() / n;
            Some((1.0 + avg_daily).powf(trading_days) - 1.0)
        }
        ProfitRateCalculationType::Logarithm => {
            let avg_daily = daily_returns.iter().sum::<f64>() / n;
            Some(avg_daily * trading_days)
        }
    }
}

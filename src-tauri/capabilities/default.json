{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "opener:allow-open-path", "fs:allow-resource-write-recursive", {"identifier": "fs:allow-mkdir", "allow": [{"path": "$APPDATA/*"}, {"path": "$DOCUMENT/*"}]}, {"identifier": "fs:allow-create", "allow": [{"path": "$APPDATA/*"}, {"path": "$DOCUMENT/*"}]}, {"identifier": "fs:allow-read-dir", "allow": [{"path": "$APPDATA/*"}, {"path": "$DOCUMENT/*"}]}, {"identifier": "fs:allow-read-file", "allow": [{"path": "$APPDATA/*"}, {"path": "$DOCUMENT/*"}]}, {"identifier": "fs:allow-write-file", "allow": [{"path": "$APPDATA/*"}, {"path": "$DOCUMENT/*"}]}]}